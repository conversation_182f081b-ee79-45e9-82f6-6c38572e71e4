using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NetworkManagement.Models
{
    public class Site
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? Address { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        public double? GpsLat { get; set; }

        public double? GpsLng { get; set; }

        [MaxLength(100)]
        public string? PowerSource { get; set; }

        public int? StorageCapacity { get; set; }

        public int? DailyConsumption { get; set; }

        [MaxLength(100)]
        public string? InstallationBase { get; set; }

        public int? Boxes { get; set; }

        public int? WireLength { get; set; }

        public string? AssociatedDeviceIds { get; set; } // JSON string

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Device> Devices { get; set; } = new List<Device>();

        // Display properties
        public string CoordinatesDisplay =>
            GpsLat.HasValue && GpsLng.HasValue
                ? $"{GpsLat:F6}, {GpsLng:F6}"
                : "غير محدد";

        public string StorageCapacityDisplay => StorageCapacity?.ToString() + " وحدة" ?? "غير محدد";
        public string DailyConsumptionDisplay => DailyConsumption?.ToString() + " وحدة/يوم" ?? "غير محدد";
        public string WireLengthDisplay => WireLength?.ToString() + " متر" ?? "غير محدد";
    }
}
