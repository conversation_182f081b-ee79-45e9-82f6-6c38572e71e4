<Window x:Class="NetworkManagement.Views.SiteDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding Title}"
        Height="550" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        Foreground="{DynamicResource MaterialDesignBody}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Site Name -->
                <TextBox materialDesign:HintAssist.Hint="اسم الموقع *"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Address -->
                <TextBox materialDesign:HintAssist.Hint="العنوان"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Phone -->
                <TextBox materialDesign:HintAssist.Hint="رقم الهاتف"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- GPS Coordinates -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                             materialDesign:HintAssist.Hint="خط العرض (GPS)"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding GpsLat, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"/>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="خط الطول (GPS)"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding GpsLng, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- Power Source -->
                <TextBox materialDesign:HintAssist.Hint="مصدر الطاقة"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding PowerSource, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Storage and Consumption -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                             materialDesign:HintAssist.Hint="سعة التخزين"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding StorageCapacity, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"/>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="الاستهلاك اليومي"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding DailyConsumption, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- Installation Base -->
                <TextBox materialDesign:HintAssist.Hint="قاعدة التركيب"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding InstallationBase, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Boxes and Wire Length -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                             materialDesign:HintAssist.Hint="عدد الصناديق"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding Boxes, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"/>

                    <TextBox Grid.Column="1"
                             materialDesign:HintAssist.Hint="طول السلك"
                             materialDesign:HintAssist.IsFloating="True"
                             Text="{Binding WireLength, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,0"/>
                </Grid>

                <!-- Network -->
                <ComboBox materialDesign:HintAssist.Hint="الشبكة (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding AvailableNetworks}"
                         SelectedValuePath="Id"
                         DisplayMemberPath="Name"
                         SelectedValue="{Binding SelectedNetworkId}"
                         Margin="0,0,0,20"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                           Foreground="{DynamicResource ValidationErrorBrush}"
                           FontWeight="Medium"
                           Margin="0,0,0,10"
                           Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Required Fields Note -->
                <TextBlock Text="* الحقول المطلوبة"
                           FontSize="12"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           Margin="0,10,0,0"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="1"
                Background="{DynamicResource MaterialDesignPaper}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="0,1,0,0"
                Padding="20">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Column="0"
                             IsIndeterminate="True"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                             VerticalAlignment="Center"
                             Height="4"
                             Margin="0,0,20,0"/>

                <!-- Cancel Button -->
                <Button Grid.Column="1"
                        Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,10,0"
                        MinWidth="80"/>

                <!-- Save Button -->
                <Button Grid.Column="2"
                        Content="{Binding IsEditMode, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='تحديث|حفظ'}"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                        MinWidth="80"/>
            </Grid>
        </Border>
    </Grid>
</Window>
