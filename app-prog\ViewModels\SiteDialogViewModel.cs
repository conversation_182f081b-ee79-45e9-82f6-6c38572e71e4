using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.ViewModels
{
    public partial class SiteDialogViewModel : ObservableObject
    {
        private readonly ISiteService _siteService;
        private readonly IAuthService _authService;
        private readonly INetworkService _networkService;

        [ObservableProperty]
        private string title = "إضافة موقع جديد";

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string address = string.Empty;

        [ObservableProperty]
        private string phone = string.Empty;

        [ObservableProperty]
        private double? gpsLat;

        [ObservableProperty]
        private double? gpsLng;

        [ObservableProperty]
        private string powerSource = string.Empty;

        [ObservableProperty]
        private int? storageCapacity;

        [ObservableProperty]
        private int? dailyConsumption;

        [ObservableProperty]
        private string installationBase = string.Empty;

        [ObservableProperty]
        private int? boxes;

        [ObservableProperty]
        private int? wireLength;

        [ObservableProperty]
        private string selectedNetworkId = string.Empty;

        [ObservableProperty]
        private List<Network> availableNetworks = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        private Site? _editingSite;
        public bool IsEditMode => _editingSite != null;

        public event EventHandler<Site>? SiteSaved;
        public event EventHandler? DialogClosed;

        public SiteDialogViewModel(ISiteService siteService, IAuthService authService, INetworkService networkService)
        {
            _siteService = siteService;
            _authService = authService;
            _networkService = networkService;
            _ = LoadNetworksAsync();
        }

        public void SetEditSite(Site site)
        {
            _editingSite = site;
            Title = "تحرير الموقع";

            Name = site.Name;
            Address = site.Address ?? string.Empty;
            Phone = site.Phone ?? string.Empty;
            GpsLat = site.GpsLat;
            GpsLng = site.GpsLng;
            PowerSource = site.PowerSource ?? string.Empty;
            StorageCapacity = site.StorageCapacity;
            DailyConsumption = site.DailyConsumption;
            InstallationBase = site.InstallationBase ?? string.Empty;
            Boxes = site.Boxes;
            WireLength = site.WireLength;
            SelectedNetworkId = site.NetworkId ?? string.Empty;
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SaveAsync()
        {
            try
            {
                if (!ValidateInput())
                    return;

                IsLoading = true;
                ErrorMessage = string.Empty;

                var site = _editingSite ?? new Site();

                site.Name = Name.Trim();
                site.Address = string.IsNullOrWhiteSpace(Address) ? null : Address.Trim();
                site.Phone = string.IsNullOrWhiteSpace(Phone) ? null : Phone.Trim();
                site.GpsLat = GpsLat;
                site.GpsLng = GpsLng;
                site.PowerSource = string.IsNullOrWhiteSpace(PowerSource) ? null : PowerSource.Trim();
                site.StorageCapacity = StorageCapacity;
                site.DailyConsumption = DailyConsumption;
                site.InstallationBase = string.IsNullOrWhiteSpace(InstallationBase) ? null : InstallationBase.Trim();
                site.Boxes = Boxes;
                site.WireLength = WireLength;
                // تعيين NetworkId - تعيين شبكة المستخدم تلقائ<|im_start|> للـ Network Manager
                if (!string.IsNullOrWhiteSpace(SelectedNetworkId))
                {
                    // استخدام الشبكة المختارة
                    site.NetworkId = SelectedNetworkId;
                }
                else
                {
                    // إذا كان المستخدم Network Manager، استخدم شبكته تلقائ<|im_start|>
                    var currentUser = _authService.CurrentUser;
                    if (_authService.IsAdmin)
                    {
                        // للأدمن: يمكن ترك الحقل فارغ
                        site.NetworkId = null;
                    }
                    else if (_authService.IsNetworkManager && !string.IsNullOrEmpty(currentUser?.NetworkId))
                    {
                        // للـ Network Manager: استخدم شبكته تلقائياً
                        site.NetworkId = currentUser.NetworkId;
                    }
                    else
                    {
                        // لباقي المستخدمين: يمكن ترك الحقل فارغ
                        site.NetworkId = null;
                    }
                }

                if (_editingSite == null)
                {
                    // Adding new site
                    site.CreatedAt = DateTime.Now;
                    await _siteService.CreateAsync(site);
                }
                else
                {
                    // Updating existing site
                    await _siteService.UpdateAsync(site);
                }

                SiteSaved?.Invoke(this, site);
                DialogClosed?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ الموقع: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }

        private bool ValidateInput()
        {
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(Name))
            {
                ErrorMessage = "اسم الموقع مطلوب";
                return false;
            }

            // Validate Phone format if provided
            if (!string.IsNullOrWhiteSpace(Phone))
            {
                if (Phone.Trim().Length < 7)
                {
                    ErrorMessage = "رقم الهاتف قصير جداً";
                    return false;
                }
            }

            return true;
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                // استخدام NetworkHelper لتحميل الشبكات مع تطبيق الصلاحيات
                var (networks, defaultNetworkId) = await NetworkHelper.LoadNetworksForSelectionAsync(
                    _authService,
                    _networkService);

                AvailableNetworks = networks;

                // تعيين الشبكة الافتراضية
                if (!string.IsNullOrEmpty(defaultNetworkId))
                {
                    SelectedNetworkId = defaultNetworkId;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks: {ex.Message}");
            }
        }
    }
}
