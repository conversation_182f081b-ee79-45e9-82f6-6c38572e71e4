<UserControl x:Class="NetworkManagement.Views.NetworkManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📡" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة الشبكات" FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBox x:Name="SearchBox"
                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Width="200" Height="35" Margin="0,0,10,0"
                             VerticalContentAlignment="Center"
                             Background="{DynamicResource MaterialDesignPaper}"
                             BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1"
                             FontSize="14">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                    <VisualBrush.Visual>
                                                        <TextBlock Text="🔍 البحث في الشبكات..."
                                                                   Foreground="{DynamicResource MaterialDesignBodyLight}" FontSize="14" Margin="5,0"/>
                                                    </VisualBrush.Visual>
                                                </VisualBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>

                    <Button Content="➕ إضافة شبكة"
                            Command="{Binding AddNetworkCommand}"
                            Background="{DynamicResource SecondaryHueMidBrush}" Foreground="White"
                            BorderThickness="0" Padding="15,8"
                            FontSize="14" FontWeight="Bold"
                            Cursor="Hand"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <!-- Networks List -->
            <Border Grid.Column="0" Background="{DynamicResource MaterialDesignPaper}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1"
                    CornerRadius="8" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- List Header -->
                    <Border Grid.Row="0" Background="{DynamicResource MaterialDesignCardBackground}" Padding="15,10"
                            BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,0,1">
                        <TextBlock Text="قائمة الشبكات" FontSize="16" FontWeight="Bold"
                                   Foreground="{DynamicResource MaterialDesignBody}"/>
                    </Border>

                    <!-- Networks ListBox -->
                    <ListBox Grid.Row="1" x:Name="NetworksListBox"
                             ItemsSource="{Binding Networks}"
                             SelectedItem="{Binding SelectedNetwork}"
                             Background="Transparent" BorderThickness="0"
                             ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{DynamicResource MaterialDesignCardBackground}" Margin="5" Padding="15,10"
                                        BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1" CornerRadius="5">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                                    <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Network Color -->
                                        <Ellipse Grid.Column="0" Width="20" Height="20"
                                                 Fill="{Binding Color}" Margin="0,0,10,0"/>

                                        <!-- Network Info -->
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"
                                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                                            <TextBlock Text="{Binding Description}" FontSize="12"
                                                       Foreground="{DynamicResource MaterialDesignBodyLight}" TextTrimming="CharacterEllipsis"/>
                                        </StackPanel>

                                        <!-- Status -->
                                        <Border Grid.Column="2" Background="{Binding IsActive, Converter={StaticResource BoolToStatusColorConverter}}"
                                                CornerRadius="10" Padding="8,3">
                                            <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToStatusTextConverter}}"
                                                       Foreground="White" FontSize="10" FontWeight="Bold"/>
                                        </Border>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>

            <!-- Network Details -->
            <Border Grid.Column="1" Background="{DynamicResource MaterialDesignPaper}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1"
                    CornerRadius="8" Margin="10,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Details Header -->
                    <Border Grid.Row="0" Background="{DynamicResource MaterialDesignCardBackground}" Padding="15,10"
                            BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,0,1">
                        <TextBlock Text="تفاصيل الشبكة" FontSize="16" FontWeight="Bold"
                                   Foreground="{DynamicResource MaterialDesignBody}"/>
                    </Border>

                    <!-- Details Content -->
                    <ScrollViewer Grid.Row="1" Padding="20" VerticalScrollBarVisibility="Auto">
                        <Grid>
                            <!-- Network Details -->
                            <StackPanel>
                                <StackPanel.Style>
                                    <Style TargetType="StackPanel">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding SelectedNetwork}" Value="{x:Null}">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </StackPanel.Style>

                            <!-- Network Name -->
                            <TextBlock Text="اسم الشبكة:" FontWeight="Bold" Margin="0,0,0,5"
                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                            <TextBox Text="{Binding SelectedNetwork.Name, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15" Padding="10" FontSize="14"
                                     Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                     Foreground="{DynamicResource MaterialDesignBody}"/>

                            <!-- Network Description -->
                            <TextBlock Text="وصف الشبكة:" FontWeight="Bold" Margin="0,0,0,5"
                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                            <TextBox Text="{Binding SelectedNetwork.Description, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15" Padding="10" FontSize="14" Height="80"
                                     TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                                     Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                     Foreground="{DynamicResource MaterialDesignBody}"/>

                            <!-- Network Color -->
                            <TextBlock Text="لون الشبكة:" FontWeight="Bold" Margin="0,0,0,5"
                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBox Text="{Binding SelectedNetwork.Color, UpdateSourceTrigger=PropertyChanged}"
                                         Width="100" Padding="10" FontSize="14" Margin="0,0,10,0"
                                         Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                         Foreground="{DynamicResource MaterialDesignBody}"/>
                                <Rectangle Width="30" Height="30" Fill="{Binding SelectedNetwork.Color}"
                                           Stroke="{DynamicResource MaterialDesignDivider}" StrokeThickness="1"/>
                            </StackPanel>

                            <!-- Network Status -->
                            <CheckBox Content="الشبكة نشطة"
                                      IsChecked="{Binding SelectedNetwork.IsActive}"
                                      FontSize="14" Margin="0,0,0,15"
                                      Foreground="{DynamicResource MaterialDesignBody}"/>

                            <!-- Statistics -->
                            <TextBlock Text="الإحصائيات:" FontWeight="Bold" Margin="0,10,0,5"
                                       Foreground="{DynamicResource MaterialDesignBody}"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Border Grid.Column="0" Background="{DynamicResource PrimaryHueLightBrush}" Padding="10" Margin="0,0,5,0" CornerRadius="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="👥" FontSize="20" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding SelectedNetworkUsersCount}" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center"
                                                   Foreground="{DynamicResource MaterialDesignBody}"/>
                                        <TextBlock Text="المستخدمين" FontSize="12" HorizontalAlignment="Center"
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Border>

                                <Border Grid.Column="1" Background="{DynamicResource SecondaryHueLightBrush}" Padding="10" Margin="5,0" CornerRadius="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="📱" FontSize="20" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding SelectedNetworkDevicesCount}" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center"
                                                   Foreground="{DynamicResource MaterialDesignBody}"/>
                                        <TextBlock Text="الأجهزة" FontSize="12" HorizontalAlignment="Center"
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Border>

                                <Border Grid.Column="2" Background="{DynamicResource MaterialDesignSelection}" Padding="10" Margin="5,0,0,0" CornerRadius="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="🏢" FontSize="20" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding SelectedNetworkSitesCount}" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center"
                                                   Foreground="{DynamicResource MaterialDesignBody}"/>
                                        <TextBlock Text="المواقع" FontSize="12" HorizontalAlignment="Center"
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                            </StackPanel>

                            <!-- Empty State -->
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <StackPanel.Style>
                                    <Style TargetType="StackPanel">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding SelectedNetwork}" Value="{x:Null}">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </StackPanel.Style>

                                <TextBlock Text="📡" FontSize="48" HorizontalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                <TextBlock Text="اختر شبكة لعرض التفاصيل" FontSize="16"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </ScrollViewer>

                    <!-- Action Buttons -->
                    <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}" Padding="15,10"
                            BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,1,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding SelectedNetwork}" Value="{x:Null}">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>

                            <Button Content="💾 حفظ"
                                    Command="{Binding SaveNetworkCommand}"
                                    Background="{DynamicResource SecondaryHueMidBrush}" Foreground="White"
                                    BorderThickness="0" Padding="15,8" Margin="0,0,10,0"
                                    FontSize="14" FontWeight="Bold" Cursor="Hand"/>

                            <Button Content="{Binding SelectedNetwork.IsActive, Converter={StaticResource BoolToToggleTextConverter}}"
                                    Command="{Binding ToggleNetworkStatusCommand}"
                                    Background="{DynamicResource MaterialDesignDarkForeground}" Foreground="White"
                                    BorderThickness="0" Padding="15,8" Margin="0,0,10,0"
                                    FontSize="14" FontWeight="Bold" Cursor="Hand"/>

                            <Button Content="🗑️ حذف"
                                    Command="{Binding DeleteNetworkCommand}"
                                    Background="{DynamicResource ValidationErrorBrush}" Foreground="White"
                                    BorderThickness="0" Padding="15,8"
                                    FontSize="14" FontWeight="Bold" Cursor="Hand"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="2" Background="{DynamicResource MaterialDesignDarkBackground}" Opacity="0.8"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="جاري التحميل..." Foreground="{DynamicResource MaterialDesignDarkForeground}"
                           FontSize="16" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Error Message -->
        <Border Grid.RowSpan="2" Background="{DynamicResource ValidationErrorBrush}" Padding="15" Margin="20" VerticalAlignment="Top"
                Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}" CornerRadius="5">
            <TextBlock Text="{Binding ErrorMessage}" Foreground="White" FontSize="14" TextWrapping="Wrap"/>
        </Border>
    </Grid>
</UserControl>
