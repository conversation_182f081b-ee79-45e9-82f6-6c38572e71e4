using System;
using System.Collections.Generic;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.ViewModels
{
    public partial class InventoryDialogViewModel : ObservableObject
    {
        private readonly IInventoryService _inventoryService;
        private readonly IAuthService _authService;
        private readonly INetworkService _networkService;

        [ObservableProperty]
        private string title = "إضافة عنصر جديد";

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string category = "أجهزة";

        [ObservableProperty]
        private int quantity;

        [ObservableProperty]
        private string unit = "قطعة";

        [ObservableProperty]
        private int? minimumStock;

        [ObservableProperty]
        private int? maximumStock;

        [ObservableProperty]
        private decimal? unitPrice;

        [ObservableProperty]
        private string location = string.Empty;

        [ObservableProperty]
        private string supplier = string.Empty;

        [ObservableProperty]
        private string description = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private string selectedNetworkId = string.Empty;

        [ObservableProperty]
        private List<Network> availableNetworks = new();

        public string[] Categories { get; } = { "أجهزة", "كابلات", "أدوات", "قطع غيار", "مواد استهلاكية", "أخرى" };
        public string[] Units { get; } = { "قطعة", "متر", "كيلو", "لتر", "عبوة", "صندوق" };

        private Inventory? _editingInventory;
        public bool IsEditMode => _editingInventory != null;

        public event EventHandler<Inventory>? InventorySaved;
        public event EventHandler? DialogClosed;

        public InventoryDialogViewModel(IInventoryService inventoryService, IAuthService authService, INetworkService networkService)
        {
            _inventoryService = inventoryService;
            _authService = authService;
            _networkService = networkService;
            _ = LoadNetworksAsync();
        }

        public void SetEditInventory(Inventory inventory)
        {
            _editingInventory = inventory;
            Title = "تحرير العنصر";

            Name = inventory.Name;
            Category = inventory.Category;
            Quantity = inventory.Quantity;
            Unit = inventory.Unit ?? "قطعة";
            MinimumStock = inventory.MinimumStock;
            MaximumStock = inventory.MaximumStock;
            UnitPrice = inventory.UnitPrice;
            Location = inventory.Location ?? string.Empty;
            Supplier = inventory.Supplier ?? string.Empty;
            Description = inventory.Description ?? string.Empty;
            SelectedNetworkId = inventory.NetworkId ?? string.Empty;
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SaveAsync()
        {
            try
            {
                if (!ValidateInput())
                    return;

                IsLoading = true;
                ErrorMessage = string.Empty;

                var inventory = _editingInventory ?? new Inventory();

                inventory.Name = Name.Trim();
                inventory.Category = Category;
                inventory.Quantity = Quantity;
                inventory.Unit = string.IsNullOrWhiteSpace(Unit) ? "" : Unit.Trim();
                inventory.MinimumStock = MinimumStock;
                inventory.MaximumStock = MaximumStock;
                inventory.UnitPrice = UnitPrice;
                inventory.Location = string.IsNullOrWhiteSpace(Location) ? null : Location.Trim();
                inventory.Supplier = string.IsNullOrWhiteSpace(Supplier) ? null : Supplier.Trim();
                inventory.Description = string.IsNullOrWhiteSpace(Description) ? null : Description.Trim();

                // تعيين NetworkId - تعيين شبكة المستخدم تلقائ<|im_start|> للـ Network Manager
                if (!string.IsNullOrWhiteSpace(SelectedNetworkId))
                {
                    // استخدام الشبكة المختارة
                    inventory.NetworkId = SelectedNetworkId;
                }
                else
                {
                    // إذا كان المستخدم Network Manager، استخدم شبكته تلقائ<|im_start|>
                    var currentUser = _authService.CurrentUser;
                    if (_authService.IsAdmin)
                    {
                        // للأدمن: يمكن ترك الحقل فارغ
                        inventory.NetworkId = null;
                    }
                    else if (_authService.IsNetworkManager && !string.IsNullOrEmpty(currentUser?.NetworkId))
                    {
                        // للـ Network Manager: استخدم شبكته تلقائياً
                        inventory.NetworkId = currentUser.NetworkId;
                    }
                    else
                    {
                        // لباقي المستخدمين: يمكن ترك الحقل فارغ
                        inventory.NetworkId = null;
                    }
                }

                // التحقق من الصلاحيات قبل الحفظ
                if (_editingInventory == null)
                {
                    // التحقق من صلاحية الإضافة
                    if (!_authService.CanAddData(inventory.NetworkId))
                    {
                        ErrorMessage = "ليس لديك صلاحية لإضافة عناصر مخزون في هذه الشبكة";
                        return;
                    }

                    // Adding new inventory item
                    await _inventoryService.CreateAsync(inventory);
                }
                else
                {
                    // التحقق من صلاحية التعديل
                    if (!_authService.CanEditData(inventory.NetworkId))
                    {
                        ErrorMessage = "ليس لديك صلاحية لتعديل هذا العنصر";
                        return;
                    }

                    // Updating existing inventory item
                    await _inventoryService.UpdateAsync(inventory);
                }

                InventorySaved?.Invoke(this, inventory);
                DialogClosed?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ العنصر: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }

        private bool ValidateInput()
        {
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(Name))
            {
                ErrorMessage = "اسم العنصر مطلوب";
                return false;
            }

            if (Quantity < 0)
            {
                ErrorMessage = "الكمية لا يمكن أن تكون سالبة";
                return false;
            }

            if (MinimumStock.HasValue && MaximumStock.HasValue && MinimumStock > MaximumStock)
            {
                ErrorMessage = "الحد الأدنى لا يمكن أن يكون أكبر من الحد الأعلى";
                return false;
            }

            if (UnitPrice.HasValue && UnitPrice < 0)
            {
                ErrorMessage = "سعر الوحدة لا يمكن أن يكون سالباً";
                return false;
            }

            return true;
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                // استخدام NetworkHelper لتحميل الشبكات مع تطبيق الصلاحيات
                var (networks, defaultNetworkId) = await NetworkHelper.LoadNetworksForSelectionAsync(
                    _authService,
                    _networkService);

                AvailableNetworks = networks;

                // تعيين الشبكة الافتراضية
                if (!string.IsNullOrEmpty(defaultNetworkId))
                {
                    SelectedNetworkId = defaultNetworkId;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks: {ex.Message}");
            }
        }
    }
}
