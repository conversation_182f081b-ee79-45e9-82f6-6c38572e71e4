using System;
using System.Threading.Tasks;

namespace NetworkManagement.Services
{
    public interface INotificationService
    {
        /// <summary>
        /// إظهار إشعار معلومات
        /// </summary>
        Task ShowInfoAsync(string title, string message);

        /// <summary>
        /// إظهار إشعار تحذير
        /// </summary>
        Task ShowWarningAsync(string title, string message);

        /// <summary>
        /// إظهار إشعار خطأ
        /// </summary>
        Task ShowErrorAsync(string title, string message);

        /// <summary>
        /// إظهار إشعار نجاح
        /// </summary>
        Task ShowSuccessAsync(string title, string message);

        /// <summary>
        /// إظهار إشعار مخصص
        /// </summary>
        Task ShowCustomAsync(string title, string message, NotificationType type, TimeSpan? duration = null);

        /// <summary>
        /// إظهار إشعار حالة الجهاز
        /// </summary>
        Task ShowDeviceStatusAsync(string deviceName, string status, bool isOnline);

        /// <summary>
        /// إظهار إشعار مخزون منخفض
        /// </summary>
        Task ShowLowStockAsync(string itemName, int currentStock, int minimumStock);

        /// <summary>
        /// إظهار إشعار مهمة مكتملة
        /// </summary>
        Task ShowTaskCompletedAsync(string taskTitle);

        /// <summary>
        /// إظهار إشعار نسخة احتياطية
        /// </summary>
        Task ShowBackupNotificationAsync(bool success, string message);

        /// <summary>
        /// مسح جميع الإشعارات
        /// </summary>
        void ClearAllNotifications();

        /// <summary>
        /// تفعيل أو إلغاء تفعيل الإشعارات
        /// </summary>
        void SetNotificationsEnabled(bool enabled);
    }

    public enum NotificationType
    {
        Info,
        Warning,
        Error,
        Success
    }
}
