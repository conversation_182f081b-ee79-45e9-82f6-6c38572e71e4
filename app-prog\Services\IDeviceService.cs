using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface IDeviceService
    {
        Task<IEnumerable<Device>> GetAllAsync(string? networkFilter = null);
        Task<Device?> GetByIdAsync(string id);
        Task<Device> CreateAsync(Device device);
        Task<Device> UpdateAsync(Device device);
        Task<bool> DeleteAsync(string id);
        Task<IEnumerable<Device>> SearchAsync(string searchTerm, string? networkFilter = null);
        Task<IEnumerable<Device>> GetBySiteIdAsync(string siteId);
        Task<IEnumerable<Device>> GetByStatusAsync(string status, string? networkFilter = null);
        Task<Device> UpdateDeviceStatusAsync(string deviceId, string status);
        Task<int> UpdateMultipleDeviceStatusesAsync(Dictionary<string, string> deviceStatuses);
        Task<IEnumerable<Device>> GetDevicesWithIpAsync();
    }
}
