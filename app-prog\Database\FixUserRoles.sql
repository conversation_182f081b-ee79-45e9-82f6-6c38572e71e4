-- إصلاح أسماء الأدوار في قاعدة البيانات
-- تحديث جميع الأدوار لتوحيد التسمية مع الكود

USE NetworkManagementDB;

-- تحديث الأدوار الموجودة لتوحيد التسمية
UPDATE Users SET Role = 'Admin' WHERE Role IN ('admin', 'ADMIN', 'administrator', 'Administrator');
UPDATE Users SET Role = 'Manager' WHERE Role IN ('manager', 'MANAGER');
UPDATE Users SET Role = 'Technician' WHERE Role IN ('technician', 'TECHNICIAN', 'tech', 'Tech');
UPDATE Users SET Role = 'User' WHERE Role IN ('user', 'USER', 'normal', 'Normal');

-- عرض النتائج بعد التحديث
SELECT Role, COUNT(*) as Count FROM Users GROUP BY Role;
