using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.Converters
{
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            return false;
        }
    }

    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Collapsed;
            }
            return true;
        }
    }

    public class InverseBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }

    public class StringToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var str = value?.ToString();
            var param = parameter?.ToString();

            if (param == "Inverse")
            {
                return string.IsNullOrWhiteSpace(str) ? Visibility.Visible : Visibility.Collapsed;
            }
            else if (param == "NotEmpty")
            {
                return !string.IsNullOrWhiteSpace(str);
            }

            return string.IsNullOrWhiteSpace(str) ? Visibility.Collapsed : Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class ZeroToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int intValue)
            {
                bool isInverse = parameter?.ToString() == "Inverse";
                if (isInverse)
                {
                    return intValue > 0 ? Visibility.Visible : Visibility.Collapsed;
                }
                else
                {
                    return intValue == 0 ? Visibility.Visible : Visibility.Collapsed;
                }
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }



    public class BooleanToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string paramString)
            {
                var parts = paramString.Split('|');
                if (parts.Length == 2)
                {
                    return boolValue ? parts[0] : parts[1];
                }
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BooleanToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string paramString)
            {
                var colors = paramString.Split(',');
                if (colors.Length == 2)
                {
                    var colorName = boolValue ? colors[0] : colors[1];
                    return colorName switch
                    {
                        "Green" => System.Windows.Media.Brushes.Green,
                        "Red" => System.Windows.Media.Brushes.Red,
                        "Blue" => System.Windows.Media.Brushes.Blue,
                        "Orange" => System.Windows.Media.Brushes.Orange,
                        "Gray" => System.Windows.Media.Brushes.Gray,
                        "LightGreen" => System.Windows.Media.Brushes.LightGreen,
                        "LightCoral" => System.Windows.Media.Brushes.LightCoral,
                        _ => System.Windows.Media.Brushes.Black
                    };
                }
            }
            return System.Windows.Media.Brushes.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter للتحقق من صلاحية تعديل البيانات
    /// </summary>
    public class CanEditDataConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var authService = App.GetService<IAuthService>();
                if (authService == null || !authService.IsLoggedIn)
                    return Visibility.Collapsed;

                string? networkId = null;

                // إذا كان value هو كائن له NetworkId
                if (value != null)
                {
                    var networkIdProperty = value.GetType().GetProperty("NetworkId");
                    if (networkIdProperty != null)
                    {
                        networkId = networkIdProperty.GetValue(value)?.ToString();
                    }
                }

                // التحقق من الصلاحية العامة أولاً ثم الصلاحية المحددة للبيانات
                bool canEdit = authService.CanManageDevices && authService.CanEditData(networkId);
                return canEdit ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CanEditDataConverter: {ex.Message}");
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter للتحقق من صلاحية حذف البيانات
    /// </summary>
    public class CanDeleteDataConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var authService = App.GetService<IAuthService>();
                if (authService == null || !authService.IsLoggedIn)
                    return Visibility.Collapsed;

                string? networkId = null;

                // إذا كان value هو كائن له NetworkId
                if (value != null)
                {
                    var networkIdProperty = value.GetType().GetProperty("NetworkId");
                    if (networkIdProperty != null)
                    {
                        networkId = networkIdProperty.GetValue(value)?.ToString();
                    }
                }

                // التحقق من الصلاحية العامة أولاً ثم الصلاحية المحددة للبيانات
                bool canDelete = authService.CanManageDevices && authService.CanDeleteData(networkId);
                return canDelete ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CanDeleteDataConverter: {ex.Message}");
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter للتحقق من صلاحية تعديل المستخدمين
    /// </summary>
    public class CanEditUserConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var authService = App.GetService<IAuthService>();
                if (authService == null) return Visibility.Collapsed;

                if (value is User user)
                {
                    return authService.CanEditUser(user) ? Visibility.Visible : Visibility.Collapsed;
                }

                return Visibility.Collapsed;
            }
            catch
            {
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter للتحقق من صلاحية حذف المستخدمين
    /// </summary>
    public class CanDeleteUserConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var authService = App.GetService<IAuthService>();
                if (authService == null) return Visibility.Collapsed;

                if (value is User user)
                {
                    return authService.CanDeleteUser(user) ? Visibility.Visible : Visibility.Collapsed;
                }

                return Visibility.Collapsed;
            }
            catch
            {
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول للتحقق من صلاحية عرض البيانات
    /// </summary>
    public class CanViewDataConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var authService = App.GetService<IAuthService>();
                if (authService == null) return Visibility.Collapsed;

                string? networkId = null;

                // إذا كان value هو كائن له NetworkId
                if (value != null)
                {
                    var networkIdProperty = value.GetType().GetProperty("NetworkId");
                    if (networkIdProperty != null)
                    {
                        networkId = networkIdProperty.GetValue(value)?.ToString();
                    }
                }

                return authService.CanViewData(networkId) ? Visibility.Visible : Visibility.Collapsed;
            }
            catch
            {
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول عام للصلاحيات يأخذ نوع الصلاحية كمعامل
    /// </summary>
    public class PermissionConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var authService = App.GetService<IAuthService>();
                if (authService == null) return Visibility.Collapsed;

                var permissionType = parameter?.ToString();

                switch (permissionType?.ToLower())
                {
                    case "canadd":
                        return authService.CanAddData() ? Visibility.Visible : Visibility.Collapsed;
                    case "canedit":
                        return authService.CanEditData() ? Visibility.Visible : Visibility.Collapsed;
                    case "candelete":
                        return authService.CanDeleteData() ? Visibility.Visible : Visibility.Collapsed;
                    case "canmanage":
                        return authService.CanManageDevices ? Visibility.Visible : Visibility.Collapsed;
                    case "canmanageusers":
                        return authService.CanManageUsers ? Visibility.Visible : Visibility.Collapsed;
                    case "canmanagenetworks":
                        return authService.CanManageNetworks ? Visibility.Visible : Visibility.Collapsed;
                    case "canviewsettings":
                        return authService.CanViewSettings ? Visibility.Visible : Visibility.Collapsed;
                    default:
                        return Visibility.Collapsed;
                }
            }
            catch
            {
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }



    /// <summary>
    /// محول لتبديل النص حسب القيمة المنطقية
    /// </summary>
    public class BoolToToggleTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string paramString)
            {
                var parts = paramString.Split('|');
                if (parts.Length == 2)
                {
                    return boolValue ? parts[0] : parts[1];
                }
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول لتحويل القيمة المنطقية إلى لون الحالة
    /// </summary>
    public class BoolToStatusColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Red;
            }
            return System.Windows.Media.Brushes.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول لتحويل القيمة المنطقية إلى نص الحالة
    /// </summary>
    public class BoolToStatusTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? "متصل" : "غير متصل";
            }
            return "غير معروف";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
