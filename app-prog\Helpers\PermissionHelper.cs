using System;
using System.Collections.Generic;
using System.Linq;
using NetworkManagement.Services;

namespace NetworkManagement.Helpers
{
    /// <summary>
    /// فئة مساعدة لإزالة التكرار في منطق فلترة الصلاحيات
    /// </summary>
    public static class PermissionHelper
    {
        /// <summary>
        /// تطبيق فلترة الصلاحيات على مجموعة من البيانات
        /// </summary>
        /// <typeparam name="T">نوع البيانات التي تحتوي على NetworkId</typeparam>
        /// <param name="data">البيانات المراد فلترتها</param>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="getNetworkId">دالة للحصول على NetworkId من العنصر</param>
        /// <returns>البيانات المفلترة حسب الصلاحيات</returns>
        public static IEnumerable<T> ApplyPermissionFilter<T>(
            IEnumerable<T> data, 
            IAuthService authService, 
            Func<T, string?> getNetworkId)
        {
            if (authService.CanViewAllNetworks)
            {
                return data;
            }

            return data.Where(item => authService.CanViewData(getNetworkId(item)));
        }

        /// <summary>
        /// الحصول على فلتر الشبكة للاستعلامات
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <returns>معرف الشبكة للفلترة أو null للعرض الكامل</returns>
        public static string? GetNetworkFilter(IAuthService authService)
        {
            return authService.CanViewAllNetworks ? null : authService.CurrentUserNetworkId;
        }

        /// <summary>
        /// التحقق من صلاحية تعديل عنصر معين
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="networkId">معرف الشبكة للعنصر</param>
        /// <returns>true إذا كان يمكن التعديل</returns>
        public static bool CanEditItem(IAuthService authService, string? networkId)
        {
            return authService.CanEditData(networkId);
        }

        /// <summary>
        /// التحقق من صلاحية حذف عنصر معين
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="networkId">معرف الشبكة للعنصر</param>
        /// <returns>true إذا كان يمكن الحذف</returns>
        public static bool CanDeleteItem(IAuthService authService, string? networkId)
        {
            return authService.CanDeleteData(networkId);
        }

        /// <summary>
        /// التحقق من صلاحية إضافة عنصر في شبكة معينة
        /// </summary>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="targetNetworkId">معرف الشبكة المستهدفة</param>
        /// <returns>true إذا كان يمكن الإضافة</returns>
        public static bool CanAddItem(IAuthService authService, string? targetNetworkId = null)
        {
            return authService.CanAddData(targetNetworkId);
        }

        /// <summary>
        /// عرض رسالة رفض الصلاحية
        /// </summary>
        /// <param name="action">نوع العملية (إضافة، تعديل، حذف)</param>
        /// <param name="item">نوع العنصر</param>
        public static void ShowPermissionDeniedMessage(string action, string item)
        {
            System.Windows.MessageBox.Show(
                $"ليس لديك صلاحية {action} {item}",
                "غير مسموح",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Warning);
        }

        /// <summary>
        /// تطبيق فلترة الصلاحيات مع تحميل البيانات من الخدمة
        /// </summary>
        /// <typeparam name="T">نوع البيانات</typeparam>
        /// <param name="authService">خدمة المصادقة</param>
        /// <param name="dataLoader">دالة تحميل البيانات</param>
        /// <param name="getNetworkId">دالة للحصول على NetworkId</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>البيانات المفلترة</returns>
        public static async System.Threading.Tasks.Task<IEnumerable<T>> LoadFilteredDataAsync<T>(
            IAuthService authService,
            Func<string?, System.Threading.Tasks.Task<IEnumerable<T>>> dataLoader,
            Func<T, string?> getNetworkId,
            System.Threading.CancellationToken cancellationToken = default)
        {
            // إذا كان المستخدم Super Admin، تحميل جميع البيانات بدون فلتر
            if (authService.CanViewAllNetworks)
            {
                var allData = await dataLoader(null).ConfigureAwait(false);
                cancellationToken.ThrowIfCancellationRequested();
                return allData;
            }

            // للمستخدمين الآخرين، تطبيق فلتر الشبكة على مستوى قاعدة البيانات فقط
            var networkFilter = authService.CurrentUserNetworkId;
            var data = await dataLoader(networkFilter).ConfigureAwait(false);

            // التحقق من الإلغاء قبل الإرجاع
            cancellationToken.ThrowIfCancellationRequested();

            return data;
        }

        /// <summary>
        /// تطبيق فلترة الصلاحيات مع تحميل البيانات من الخدمة (النسخة القديمة للتوافق)
        /// </summary>
        public static async System.Threading.Tasks.Task<IEnumerable<T>> LoadFilteredDataAsync<T>(
            IAuthService authService,
            Func<string?, System.Threading.Tasks.Task<IEnumerable<T>>> dataLoader,
            Func<T, string?> getNetworkId)
        {
            return await LoadFilteredDataAsync(authService, dataLoader, getNetworkId, System.Threading.CancellationToken.None);
        }
    }
}
