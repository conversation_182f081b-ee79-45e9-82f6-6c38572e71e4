using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Microsoft.Win32;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using OfficeOpenXml.Drawing.Chart;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using NetworkManagement.Models;
using System.Drawing;
using Color = System.Drawing.Color;
using Task = System.Threading.Tasks.Task;
using Brushes = System.Windows.Media.Brushes;
using Colors = QuestPDF.Helpers.Colors;

namespace NetworkManagement.Services
{
    public class ProfessionalReportService : IProfessionalReportService
    {
        private readonly IAuthService _authService;
        private readonly IDeviceService _deviceService;
        private readonly IUserService _userService;
        private readonly ITaskService _taskService;
        private readonly ISiteService _siteService;
        private readonly IInventoryService _inventoryService;
        private readonly IPurchaseService _purchaseService;

        public ProfessionalReportService(
            IAuthService authService,
            IDeviceService deviceService,
            IUserService userService,
            ITaskService taskService,
            ISiteService siteService,
            IInventoryService inventoryService,
            IPurchaseService purchaseService)
        {
            _authService = authService;
            _deviceService = deviceService;
            _userService = userService;
            _taskService = taskService;
            _siteService = siteService;
            _inventoryService = inventoryService;
            _purchaseService = purchaseService;

            // تهيئة QuestPDF
            QuestPDF.Settings.License = LicenseType.Community;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        #region Excel Reports

        public async Task<string> GenerateDevicesExcelReportAsync(IEnumerable<Device> devices, string filePath)
        {
            return await Task.Run(() =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير الأجهزة");

                // إعداد الرأس
                SetupExcelHeader(worksheet, "تقرير الأجهزة", "إدارة الشبكات");

                // رأس الجدول
                var headers = new[] { "المسؤول", "نوع الجهاز", "الموقع", "عنوان IP", "الحالة", "تاريخ التثبيت", "آخر فحص", "الشبكة" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[6, i + 1].Value = headers[i];
                }

                // تنسيق رأس الجدول
                var headerRange = worksheet.Cells[6, 1, 6, headers.Length];
                FormatExcelHeader(headerRange);

                // إضافة البيانات
                var deviceList = devices.ToList();
                for (int i = 0; i < deviceList.Count; i++)
                {
                    var device = deviceList[i];
                    var row = i + 7;

                    worksheet.Cells[row, 1].Value = device.Responsible ?? "غير محدد";
                    worksheet.Cells[row, 2].Value = device.Type ?? "غير محدد";
                    worksheet.Cells[row, 3].Value = device.Location ?? "غير محدد";
                    worksheet.Cells[row, 4].Value = device.Ip ?? "غير محدد";
                    worksheet.Cells[row, 5].Value = device.StatusDisplay;
                    worksheet.Cells[row, 6].Value = device.InstallDateDisplay;
                    worksheet.Cells[row, 7].Value = device.LastCheckDisplay;
                    worksheet.Cells[row, 8].Value = device.Network?.Name ?? "غير محدد";

                    // تلوين الصفوف
                    if (i % 2 == 0)
                    {
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(240, 248, 255));
                    }

                    // تلوين حسب الحالة
                    var statusColor = GetStatusColor(device.Status ?? "");
                    if (statusColor != Color.Transparent)
                    {
                        worksheet.Cells[row, 5].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 5].Style.Fill.BackgroundColor.SetColor(statusColor);
                    }
                }

                // تنسيق عام
                FormatExcelWorksheet(worksheet, deviceList.Count + 6, headers.Length);

                // إضافة إحصائيات
                AddDeviceStatistics(worksheet, deviceList, headers.Length + 2);

                // حفظ الملف
                package.SaveAs(new FileInfo(filePath));
                return filePath;
            });
        }

        public async Task<string> GenerateUsersExcelReportAsync(IEnumerable<User> users, string filePath)
        {
            return await Task.Run(() =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير المستخدمين");

                SetupExcelHeader(worksheet, "تقرير المستخدمين", "إدارة الشبكات");

                var headers = new[] { "اسم المستخدم", "الاسم الكامل", "الدور", "الشبكة", "البريد الإلكتروني", "الحالة", "تاريخ الإنشاء" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[6, i + 1].Value = headers[i];
                }

                var headerRange = worksheet.Cells[6, 1, 6, headers.Length];
                FormatExcelHeader(headerRange);

                var userList = users.ToList();
                for (int i = 0; i < userList.Count; i++)
                {
                    var user = userList[i];
                    var row = i + 7;

                    worksheet.Cells[row, 1].Value = user.Username;
                    worksheet.Cells[row, 2].Value = user.Name;
                    worksheet.Cells[row, 3].Value = user.RoleDisplay;
                    worksheet.Cells[row, 4].Value = user.NetworkName;
                    worksheet.Cells[row, 5].Value = user.Email ?? "غير محدد";
                    worksheet.Cells[row, 6].Value = user.IsActive ? "نشط" : "غير نشط";
                    worksheet.Cells[row, 7].Value = user.CreatedAt.ToString("yyyy/MM/dd");

                    if (i % 2 == 0)
                    {
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(240, 248, 255));
                    }
                }

                FormatExcelWorksheet(worksheet, userList.Count + 6, headers.Length);
                AddUserStatistics(worksheet, userList, headers.Length + 2);

                package.SaveAs(new FileInfo(filePath));
                return filePath;
            });
        }

        public async Task<string> GenerateTasksExcelReportAsync(IEnumerable<NetworkManagement.Models.Task> tasks, string filePath)
        {
            return await Task.Run(() =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير المهام");

                SetupExcelHeader(worksheet, "تقرير المهام", "إدارة الشبكات");

                var headers = new[] { "الوصف", "الحالة", "الأولوية", "المستخدم", "الشبكة", "تاريخ الاستحقاق", "تاريخ الإنشاء", "تاريخ الإكمال" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[6, i + 1].Value = headers[i];
                }

                var headerRange = worksheet.Cells[6, 1, 6, headers.Length];
                FormatExcelHeader(headerRange);

                var taskList = tasks.ToList();
                for (int i = 0; i < taskList.Count; i++)
                {
                    var task = taskList[i];
                    var row = i + 7;

                    worksheet.Cells[row, 1].Value = task.Description;
                    worksheet.Cells[row, 2].Value = task.StatusDisplay;
                    worksheet.Cells[row, 3].Value = task.PriorityDisplay;
                    worksheet.Cells[row, 4].Value = task.User?.Name ?? "غير محدد";
                    worksheet.Cells[row, 5].Value = task.Network?.Name ?? "غير محدد";
                    worksheet.Cells[row, 6].Value = task.DueDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
                    worksheet.Cells[row, 7].Value = task.CreatedAt.ToString("yyyy/MM/dd");
                    worksheet.Cells[row, 8].Value = task.CompletedAt?.ToString("yyyy/MM/dd") ?? "غير مكتمل";

                    if (i % 2 == 0)
                    {
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(240, 248, 255));
                    }

                    // تلوين حسب الأولوية
                    var priorityColor = GetPriorityColor(task.Priority ?? "");
                    if (priorityColor != Color.Transparent)
                    {
                        worksheet.Cells[row, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 3].Style.Fill.BackgroundColor.SetColor(priorityColor);
                    }
                }

                FormatExcelWorksheet(worksheet, taskList.Count + 6, headers.Length);
                AddTaskStatistics(worksheet, taskList, headers.Length + 2);

                package.SaveAs(new FileInfo(filePath));
                return filePath;
            });
        }

        public async Task<string> GenerateSitesExcelReportAsync(IEnumerable<Site> sites, string filePath)
        {
            return await Task.Run(() =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير المواقع");

                SetupExcelHeader(worksheet, "تقرير المواقع", "إدارة الشبكات");

                var headers = new[] { "اسم الموقع", "العنوان", "الشبكة", "مصدر الطاقة", "سعة التخزين", "الاستهلاك اليومي", "تاريخ الإنشاء" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[6, i + 1].Value = headers[i];
                }

                var headerRange = worksheet.Cells[6, 1, 6, headers.Length];
                FormatExcelHeader(headerRange);

                var siteList = sites.ToList();
                for (int i = 0; i < siteList.Count; i++)
                {
                    var site = siteList[i];
                    var row = i + 7;

                    worksheet.Cells[row, 1].Value = site.Name;
                    worksheet.Cells[row, 2].Value = site.Address ?? "غير محدد";
                    worksheet.Cells[row, 3].Value = site.Network?.Name ?? "غير محدد";
                    worksheet.Cells[row, 4].Value = site.PowerSource ?? "غير محدد";
                    worksheet.Cells[row, 5].Value = site.StorageCapacity?.ToString() ?? "غير محدد";
                    worksheet.Cells[row, 6].Value = site.DailyConsumption?.ToString() ?? "غير محدد";
                    worksheet.Cells[row, 7].Value = site.CreatedAt.ToString("yyyy/MM/dd");

                    if (i % 2 == 0)
                    {
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(240, 248, 255));
                    }
                }

                FormatExcelWorksheet(worksheet, siteList.Count + 6, headers.Length);
                AddSiteStatistics(worksheet, siteList, headers.Length + 2);

                package.SaveAs(new FileInfo(filePath));
                return filePath;
            });
        }

        public async Task<string> GenerateInventoryExcelReportAsync(IEnumerable<Inventory> inventory, string filePath)
        {
            return await Task.Run(() =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير المخزون");

                SetupExcelHeader(worksheet, "تقرير المخزون", "إدارة الشبكات");

                var headers = new[] { "اسم العنصر", "الفئة", "الكمية", "الوحدة", "السعر", "المورد", "الحد الأدنى", "الشبكة" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[6, i + 1].Value = headers[i];
                }

                var headerRange = worksheet.Cells[6, 1, 6, headers.Length];
                FormatExcelHeader(headerRange);

                var inventoryList = inventory.ToList();
                for (int i = 0; i < inventoryList.Count; i++)
                {
                    var item = inventoryList[i];
                    var row = i + 7;

                    worksheet.Cells[row, 1].Value = item.Name;
                    worksheet.Cells[row, 2].Value = item.Category ?? "غير محدد";
                    worksheet.Cells[row, 3].Value = item.Quantity;
                    worksheet.Cells[row, 4].Value = item.Unit ?? "قطعة";
                    worksheet.Cells[row, 5].Value = item.UnitPrice?.ToString() ?? "غير محدد";
                    worksheet.Cells[row, 6].Value = item.Supplier ?? "غير محدد";
                    worksheet.Cells[row, 7].Value = item.MinimumStock?.ToString() ?? "غير محدد";
                    worksheet.Cells[row, 8].Value = item.Network?.Name ?? "غير محدد";

                    if (i % 2 == 0)
                    {
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(240, 248, 255));
                    }

                    // تحذير للمخزون المنخفض
                    if (item.MinimumStock.HasValue && item.Quantity <= item.MinimumStock.Value)
                    {
                        worksheet.Cells[row, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 3].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(255, 200, 200));
                    }
                }

                FormatExcelWorksheet(worksheet, inventoryList.Count + 6, headers.Length);
                AddInventoryStatistics(worksheet, inventoryList, headers.Length + 2);

                package.SaveAs(new FileInfo(filePath));
                return filePath;
            });
        }

        public async Task<string> GeneratePurchasesExcelReportAsync(IEnumerable<Purchase> purchases, string filePath)
        {
            return await Task.Run(() =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير المشتريات");

                SetupExcelHeader(worksheet, "تقرير المشتريات", "إدارة الشبكات");

                var headers = new[] { "نوع العنصر", "الفئة", "الكمية", "السعر", "المجموع", "المورد", "رقم الفاتورة", "التاريخ", "الشبكة" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[6, i + 1].Value = headers[i];
                }

                var headerRange = worksheet.Cells[6, 1, 6, headers.Length];
                FormatExcelHeader(headerRange);

                var purchaseList = purchases.ToList();
                for (int i = 0; i < purchaseList.Count; i++)
                {
                    var purchase = purchaseList[i];
                    var row = i + 7;

                    worksheet.Cells[row, 1].Value = purchase.ItemType;
                    worksheet.Cells[row, 2].Value = purchase.Category ?? "غير محدد";
                    worksheet.Cells[row, 3].Value = purchase.Quantity;
                    worksheet.Cells[row, 4].Value = purchase.Price.ToString();
                    worksheet.Cells[row, 5].Value = (purchase.Price * purchase.Quantity).ToString();
                    worksheet.Cells[row, 6].Value = purchase.Supplier ?? "غير محدد";
                    worksheet.Cells[row, 7].Value = purchase.InvoiceNumber ?? "غير محدد";
                    worksheet.Cells[row, 8].Value = purchase.Date.ToString("yyyy/MM/dd");
                    worksheet.Cells[row, 9].Value = purchase.Network?.Name ?? "غير محدد";

                    if (i % 2 == 0)
                    {
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 1, row, headers.Length].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(240, 248, 255));
                    }
                }

                FormatExcelWorksheet(worksheet, purchaseList.Count + 6, headers.Length);
                AddPurchaseStatistics(worksheet, purchaseList, headers.Length + 2);

                package.SaveAs(new FileInfo(filePath));
                return filePath;
            });
        }

        public async Task<string> GenerateStatisticsExcelReportAsync(string filePath)
        {
            return await Task.Run(async () =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير الإحصائيات");

                SetupExcelHeader(worksheet, "تقرير الإحصائيات العامة", "إدارة الشبكات");

                // جمع البيانات
                var devices = await _deviceService.GetAllAsync();
                var users = await _userService.GetAllAsync();
                var tasks = await _taskService.GetAllAsync();
                var sites = await _siteService.GetAllAsync();

                var deviceList = devices.ToList();
                var userList = users.ToList();
                var taskList = tasks.ToList();
                var siteList = sites.ToList();

                // إحصائيات الأجهزة
                worksheet.Cells[6, 1].Value = "إحصائيات الأجهزة";
                worksheet.Cells[6, 1].Style.Font.Bold = true;
                worksheet.Cells[6, 1].Style.Font.Size = 14;

                worksheet.Cells[7, 1].Value = "إجمالي الأجهزة:";
                worksheet.Cells[7, 2].Value = deviceList.Count;
                worksheet.Cells[8, 1].Value = "الأجهزة النشطة:";
                worksheet.Cells[8, 2].Value = deviceList.Count(d => d.Status == "active");
                worksheet.Cells[9, 1].Value = "الأجهزة غير النشطة:";
                worksheet.Cells[9, 2].Value = deviceList.Count(d => d.Status == "inactive");
                worksheet.Cells[10, 1].Value = "الأجهزة في الصيانة:";
                worksheet.Cells[10, 2].Value = deviceList.Count(d => d.Status == "maintenance");

                // إحصائيات المستخدمين
                worksheet.Cells[12, 1].Value = "إحصائيات المستخدمين";
                worksheet.Cells[12, 1].Style.Font.Bold = true;
                worksheet.Cells[12, 1].Style.Font.Size = 14;

                worksheet.Cells[13, 1].Value = "إجمالي المستخدمين:";
                worksheet.Cells[13, 2].Value = userList.Count;
                worksheet.Cells[14, 1].Value = "المستخدمين النشطين:";
                worksheet.Cells[14, 2].Value = userList.Count(u => u.IsActive);
                worksheet.Cells[15, 1].Value = "المديرين:";
                worksheet.Cells[15, 2].Value = userList.Count(u => u.Role == "Admin");
                worksheet.Cells[16, 1].Value = "المشرفين:";
                worksheet.Cells[16, 2].Value = userList.Count(u => u.Role == "Manager");

                // إحصائيات المهام
                worksheet.Cells[18, 1].Value = "إحصائيات المهام";
                worksheet.Cells[18, 1].Style.Font.Bold = true;
                worksheet.Cells[18, 1].Style.Font.Size = 14;

                worksheet.Cells[19, 1].Value = "إجمالي المهام:";
                worksheet.Cells[19, 2].Value = taskList.Count;
                worksheet.Cells[20, 1].Value = "المهام المعلقة:";
                worksheet.Cells[20, 2].Value = taskList.Count(t => t.Status == "pending");
                worksheet.Cells[21, 1].Value = "المهام قيد التنفيذ:";
                worksheet.Cells[21, 2].Value = taskList.Count(t => t.Status == "in-progress");
                worksheet.Cells[22, 1].Value = "المهام المكتملة:";
                worksheet.Cells[22, 2].Value = taskList.Count(t => t.Status == "completed");

                // إحصائيات المواقع
                worksheet.Cells[24, 1].Value = "إحصائيات المواقع";
                worksheet.Cells[24, 1].Style.Font.Bold = true;
                worksheet.Cells[24, 1].Style.Font.Size = 14;

                worksheet.Cells[25, 1].Value = "إجمالي المواقع:";
                worksheet.Cells[25, 2].Value = siteList.Count;

                // تنسيق عام
                worksheet.Cells[6, 1, 25, 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[6, 1, 25, 2].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[6, 1, 25, 2].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[6, 1, 25, 2].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                worksheet.Column(1).Width = 25;
                worksheet.Column(2).Width = 15;

                // إضافة رسم بياني للأجهزة
                AddDeviceChart(worksheet, deviceList);

                package.SaveAs(new FileInfo(filePath));
                return filePath;
            });
        }

        #endregion

        #region Helper Methods for Excel

        private void SetupExcelHeader(ExcelWorksheet worksheet, string title, string subtitle)
        {
            // عنوان التقرير
            worksheet.Cells[1, 1].Value = title;
            worksheet.Cells[1, 1].Style.Font.Size = 18;
            worksheet.Cells[1, 1].Style.Font.Bold = true;
            worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            // العنوان الفرعي
            worksheet.Cells[2, 1].Value = subtitle;
            worksheet.Cells[2, 1].Style.Font.Size = 14;
            worksheet.Cells[2, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            // تاريخ التقرير
            worksheet.Cells[3, 1].Value = $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}";
            worksheet.Cells[3, 1].Style.Font.Size = 12;
            worksheet.Cells[3, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            // المستخدم
            worksheet.Cells[4, 1].Value = $"المستخدم: {_authService.CurrentUser?.Name ?? "غير محدد"}";
            worksheet.Cells[4, 1].Style.Font.Size = 12;
            worksheet.Cells[4, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        }

        private void FormatExcelHeader(ExcelRange headerRange)
        {
            headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(68, 114, 196));
            headerRange.Style.Font.Color.SetColor(Color.White);
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Font.Size = 12;
            headerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            headerRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            headerRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            headerRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            headerRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            headerRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;
        }

        private void FormatExcelWorksheet(ExcelWorksheet worksheet, int lastRow, int lastColumn)
        {
            // تنسيق الجدول
            var tableRange = worksheet.Cells[6, 1, lastRow, lastColumn];
            tableRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;

            // تعديل عرض الأعمدة
            for (int i = 1; i <= lastColumn; i++)
            {
                worksheet.Column(i).AutoFit();
                if (worksheet.Column(i).Width > 30)
                    worksheet.Column(i).Width = 30;
                if (worksheet.Column(i).Width < 10)
                    worksheet.Column(i).Width = 10;
            }

            // تجميد الصفوف العلوية
            worksheet.View.FreezePanes(7, 1);
        }

        private Color GetStatusColor(string status)
        {
            return status?.ToLower() switch
            {
                "active" => Color.FromArgb(200, 255, 200),
                "inactive" => Color.FromArgb(255, 200, 200),
                "maintenance" => Color.FromArgb(255, 255, 200),
                _ => Color.Transparent
            };
        }

        private Color GetPriorityColor(string priority)
        {
            return priority?.ToLower() switch
            {
                "urgent" => Color.FromArgb(255, 150, 150),
                "high" => Color.FromArgb(255, 200, 150),
                "medium" => Color.FromArgb(255, 255, 150),
                "low" => Color.FromArgb(200, 255, 200),
                _ => Color.Transparent
            };
        }

        private void AddDeviceStatistics(ExcelWorksheet worksheet, List<Device> devices, int startColumn)
        {
            worksheet.Cells[6, startColumn].Value = "إحصائيات الأجهزة";
            worksheet.Cells[6, startColumn].Style.Font.Bold = true;
            worksheet.Cells[6, startColumn].Style.Font.Size = 14;

            worksheet.Cells[7, startColumn].Value = "إجمالي الأجهزة:";
            worksheet.Cells[7, startColumn + 1].Value = devices.Count;

            worksheet.Cells[8, startColumn].Value = "الأجهزة النشطة:";
            worksheet.Cells[8, startColumn + 1].Value = devices.Count(d => d.Status == "active");

            worksheet.Cells[9, startColumn].Value = "الأجهزة غير النشطة:";
            worksheet.Cells[9, startColumn + 1].Value = devices.Count(d => d.Status == "inactive");

            worksheet.Cells[10, startColumn].Value = "في الصيانة:";
            worksheet.Cells[10, startColumn + 1].Value = devices.Count(d => d.Status == "maintenance");

            worksheet.Cells[11, startColumn].Value = "لديها عنوان IP:";
            worksheet.Cells[11, startColumn + 1].Value = devices.Count(d => !string.IsNullOrEmpty(d.Ip));
        }

        private void AddUserStatistics(ExcelWorksheet worksheet, List<User> users, int startColumn)
        {
            worksheet.Cells[6, startColumn].Value = "إحصائيات المستخدمين";
            worksheet.Cells[6, startColumn].Style.Font.Bold = true;
            worksheet.Cells[6, startColumn].Style.Font.Size = 14;

            worksheet.Cells[7, startColumn].Value = "إجمالي المستخدمين:";
            worksheet.Cells[7, startColumn + 1].Value = users.Count;

            worksheet.Cells[8, startColumn].Value = "المستخدمين النشطين:";
            worksheet.Cells[8, startColumn + 1].Value = users.Count(u => u.IsActive);

            worksheet.Cells[9, startColumn].Value = "المديرين:";
            worksheet.Cells[9, startColumn + 1].Value = users.Count(u => u.Role == "Admin");

            worksheet.Cells[10, startColumn].Value = "المشرفين:";
            worksheet.Cells[10, startColumn + 1].Value = users.Count(u => u.Role == "Manager");

            worksheet.Cells[11, startColumn].Value = "الفنيين:";
            worksheet.Cells[11, startColumn + 1].Value = users.Count(u => u.Role == "Technician");
        }

        private void AddTaskStatistics(ExcelWorksheet worksheet, List<NetworkManagement.Models.Task> tasks, int startColumn)
        {
            worksheet.Cells[6, startColumn].Value = "إحصائيات المهام";
            worksheet.Cells[6, startColumn].Style.Font.Bold = true;
            worksheet.Cells[6, startColumn].Style.Font.Size = 14;

            worksheet.Cells[7, startColumn].Value = "إجمالي المهام:";
            worksheet.Cells[7, startColumn + 1].Value = tasks.Count;

            worksheet.Cells[8, startColumn].Value = "المهام المعلقة:";
            worksheet.Cells[8, startColumn + 1].Value = tasks.Count(t => t.Status == "pending");

            worksheet.Cells[9, startColumn].Value = "قيد التنفيذ:";
            worksheet.Cells[9, startColumn + 1].Value = tasks.Count(t => t.Status == "in-progress");

            worksheet.Cells[10, startColumn].Value = "المكتملة:";
            worksheet.Cells[10, startColumn + 1].Value = tasks.Count(t => t.Status == "completed");

            worksheet.Cells[11, startColumn].Value = "المهام العاجلة:";
            worksheet.Cells[11, startColumn + 1].Value = tasks.Count(t => t.Priority == "urgent");
        }

        private void AddSiteStatistics(ExcelWorksheet worksheet, List<Site> sites, int startColumn)
        {
            worksheet.Cells[6, startColumn].Value = "إحصائيات المواقع";
            worksheet.Cells[6, startColumn].Style.Font.Bold = true;
            worksheet.Cells[6, startColumn].Style.Font.Size = 14;

            worksheet.Cells[7, startColumn].Value = "إجمالي المواقع:";
            worksheet.Cells[7, startColumn + 1].Value = sites.Count;

            worksheet.Cells[8, startColumn].Value = "لديها GPS:";
            worksheet.Cells[8, startColumn + 1].Value = sites.Count(s => !string.IsNullOrEmpty(s.Address));

            worksheet.Cells[9, startColumn].Value = "لديها مصدر طاقة:";
            worksheet.Cells[9, startColumn + 1].Value = sites.Count(s => !string.IsNullOrEmpty(s.PowerSource));
        }

        private void AddInventoryStatistics(ExcelWorksheet worksheet, List<Inventory> inventory, int startColumn)
        {
            worksheet.Cells[6, startColumn].Value = "إحصائيات المخزون";
            worksheet.Cells[6, startColumn].Style.Font.Bold = true;
            worksheet.Cells[6, startColumn].Style.Font.Size = 14;

            worksheet.Cells[7, startColumn].Value = "إجمالي العناصر:";
            worksheet.Cells[7, startColumn + 1].Value = inventory.Count;

            worksheet.Cells[8, startColumn].Value = "المخزون المنخفض:";
            worksheet.Cells[8, startColumn + 1].Value = inventory.Count(i => i.MinimumStock.HasValue && i.Quantity <= i.MinimumStock.Value);

            worksheet.Cells[9, startColumn].Value = "إجمالي القيمة:";
            worksheet.Cells[9, startColumn + 1].Value = inventory.Where(i => i.UnitPrice.HasValue).Sum(i => i.Quantity * (i.UnitPrice ?? 0)).ToString();
        }

        private void AddPurchaseStatistics(ExcelWorksheet worksheet, List<Purchase> purchases, int startColumn)
        {
            worksheet.Cells[6, startColumn].Value = "إحصائيات المشتريات";
            worksheet.Cells[6, startColumn].Style.Font.Bold = true;
            worksheet.Cells[6, startColumn].Style.Font.Size = 14;

            worksheet.Cells[7, startColumn].Value = "إجمالي المشتريات:";
            worksheet.Cells[7, startColumn + 1].Value = purchases.Count;

            worksheet.Cells[8, startColumn].Value = "إجمالي المبلغ:";
            worksheet.Cells[8, startColumn + 1].Value = purchases.Sum(p => p.Price * p.Quantity).ToString();

            worksheet.Cells[9, startColumn].Value = "هذا الشهر:";
            worksheet.Cells[9, startColumn + 1].Value = purchases.Where(p => p.Date.Month == DateTime.Now.Month && p.Date.Year == DateTime.Now.Year).Sum(p => p.Price * p.Quantity).ToString();
        }

        private void AddDeviceChart(ExcelWorksheet worksheet, List<Device> devices)
        {
            try
            {
                var chart = worksheet.Drawings.AddChart("DeviceChart", eChartType.Pie);
                chart.Title.Text = "توزيع حالة الأجهزة";
                chart.SetPosition(27, 0, 1, 0);
                chart.SetSize(400, 300);

                // إضافة البيانات للرسم البياني
                var activeCount = devices.Count(d => d.Status == "active");
                var inactiveCount = devices.Count(d => d.Status == "inactive");
                var maintenanceCount = devices.Count(d => d.Status == "maintenance");

                worksheet.Cells[28, 1].Value = "نشط";
                worksheet.Cells[28, 2].Value = activeCount;
                worksheet.Cells[29, 1].Value = "غير نشط";
                worksheet.Cells[29, 2].Value = inactiveCount;
                worksheet.Cells[30, 1].Value = "صيانة";
                worksheet.Cells[30, 2].Value = maintenanceCount;

                var series = chart.Series.Add(worksheet.Cells[28, 2, 30, 2], worksheet.Cells[28, 1, 30, 1]);
                series.Header = "حالة الأجهزة";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding chart: {ex.Message}");
            }
        }

        #endregion

        #region PDF Reports

        public async Task<string> GenerateDevicesPdfReportAsync(IEnumerable<Device> devices, string filePath)
        {
            return await Task.Run(() =>
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma").FontSize(10));

                        page.Header().Column(column =>
                        {
                            column.Item().Text("تقرير الأجهزة").FontSize(20).Bold();
                            column.Item().Text("نظام إدارة الشبكات").FontSize(14);
                            column.Item().Text($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}").FontSize(10);
                            column.Item().Text($"المستخدم: {_authService.CurrentUser?.Name ?? "غير محدد"}").FontSize(10);
                        });

                        page.Content().PaddingVertical(1, Unit.Centimetre).Column(column =>
                        {
                            var deviceList = devices.ToList();

                            // إحصائيات سريعة
                            column.Item().Row(row =>
                            {
                                row.RelativeItem().Border(1).Padding(10).Column(col =>
                                {
                                    col.Item().Text("إحصائيات سريعة").FontSize(14).Bold();
                                    col.Item().Text($"إجمالي الأجهزة: {deviceList.Count}");
                                    col.Item().Text($"الأجهزة النشطة: {deviceList.Count(d => d.Status == "active")}");
                                    col.Item().Text($"الأجهزة غير النشطة: {deviceList.Count(d => d.Status == "inactive")}");
                                    col.Item().Text($"في الصيانة: {deviceList.Count(d => d.Status == "maintenance")}");
                                });
                            });

                            column.Item().PaddingVertical(10);

                            // جدول الأجهزة
                            column.Item().Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn(2); // المسؤول
                                    columns.RelativeColumn(1.5f); // النوع
                                    columns.RelativeColumn(2); // الموقع
                                    columns.RelativeColumn(1.5f); // IP
                                    columns.RelativeColumn(1); // الحالة
                                    columns.RelativeColumn(1.5f); // الشبكة
                                });

                                // رأس الجدول
                                table.Header(header =>
                                {
                                    header.Cell().Element(CellStyle).Text("المسؤول").Bold();
                                    header.Cell().Element(CellStyle).Text("النوع").Bold();
                                    header.Cell().Element(CellStyle).Text("الموقع").Bold();
                                    header.Cell().Element(CellStyle).Text("عنوان IP").Bold();
                                    header.Cell().Element(CellStyle).Text("الحالة").Bold();
                                    header.Cell().Element(CellStyle).Text("الشبكة").Bold();

                                    static IContainer CellStyle(IContainer container)
                                    {
                                        return container.DefaultTextStyle(x => x.SemiBold()).PaddingVertical(5).BorderBottom(1).BorderColor(Colors.Black);
                                    }
                                });

                                // بيانات الجدول
                                foreach (var device in deviceList)
                                {
                                    table.Cell().Element(CellStyle).Text(device.Responsible ?? "غير محدد");
                                    table.Cell().Element(CellStyle).Text(device.Type ?? "غير محدد");
                                    table.Cell().Element(CellStyle).Text(device.Location ?? "غير محدد");
                                    table.Cell().Element(CellStyle).Text(device.Ip ?? "غير محدد");
                                    table.Cell().Element(CellStyle).Text(device.StatusDisplay);
                                    table.Cell().Element(CellStyle).Text(device.Network?.Name ?? "غير محدد");

                                    static IContainer CellStyle(IContainer container)
                                    {
                                        return container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(5);
                                    }
                                }
                            });
                        });

                        page.Footer().Text("صفحة 1");
                    });
                });

                document.GeneratePdf(filePath);
                return filePath;
            });
        }

        public async Task<string> GenerateUsersPdfReportAsync(IEnumerable<User> users, string filePath)
        {
            return await Task.Run(() =>
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma").FontSize(10));

                        page.Header().Column(column =>
                        {
                            column.Item().Text("تقرير المستخدمين").FontSize(20).Bold();
                            column.Item().Text("نظام إدارة الشبكات").FontSize(14);
                            column.Item().Text($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}").FontSize(10);
                        });

                        page.Content().PaddingVertical(1, Unit.Centimetre).Column(column =>
                        {
                            var userList = users.ToList();

                            // إحصائيات
                            column.Item().Row(row =>
                            {
                                row.RelativeItem().Border(1).Padding(10).Column(col =>
                                {
                                    col.Item().Text("إحصائيات المستخدمين").FontSize(14).Bold();
                                    col.Item().Text($"إجمالي المستخدمين: {userList.Count}");
                                    col.Item().Text($"المستخدمين النشطين: {userList.Count(u => u.IsActive)}");
                                    col.Item().Text($"المديرين: {userList.Count(u => u.Role == "Admin")}");
                                    col.Item().Text($"المشرفين: {userList.Count(u => u.Role == "Manager")}");
                                });
                            });

                            column.Item().PaddingVertical(10);

                            // جدول المستخدمين
                            column.Item().Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn(2); // اسم المستخدم
                                    columns.RelativeColumn(2); // الاسم الكامل
                                    columns.RelativeColumn(1.5f); // الدور
                                    columns.RelativeColumn(2); // الشبكة
                                    columns.RelativeColumn(1); // الحالة
                                });

                                table.Header(header =>
                                {
                                    header.Cell().Element(CellStyle).Text("اسم المستخدم").Bold();
                                    header.Cell().Element(CellStyle).Text("الاسم الكامل").Bold();
                                    header.Cell().Element(CellStyle).Text("الدور").Bold();
                                    header.Cell().Element(CellStyle).Text("الشبكة").Bold();
                                    header.Cell().Element(CellStyle).Text("الحالة").Bold();

                                    static IContainer CellStyle(IContainer container)
                                    {
                                        return container.DefaultTextStyle(x => x.SemiBold()).PaddingVertical(5).BorderBottom(1).BorderColor(Colors.Black);
                                    }
                                });

                                foreach (var user in userList)
                                {
                                    table.Cell().Element(CellStyle).Text(user.Username);
                                    table.Cell().Element(CellStyle).Text(user.Name);
                                    table.Cell().Element(CellStyle).Text(user.RoleDisplay);
                                    table.Cell().Element(CellStyle).Text(user.NetworkName);
                                    table.Cell().Element(CellStyle).Text(user.IsActive ? "نشط" : "غير نشط");

                                    static IContainer CellStyle(IContainer container)
                                    {
                                        return container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(5);
                                    }
                                }
                            });
                        });

                        page.Footer().Text("صفحة 1");
                    });
                });

                document.GeneratePdf(filePath);
                return filePath;
            });
        }

        // باقي دوال PDF (مبسطة للمساحة)
        public async Task<string> GenerateTasksPdfReportAsync(IEnumerable<NetworkManagement.Models.Task> tasks, string filePath)
        {
            return await Task.Run(() =>
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma").FontSize(10));

                        page.Header().Text("تقرير المهام").FontSize(20).Bold();
                        page.Content().Column(column =>
                        {
                            foreach (var task in tasks.Take(20))
                            {
                                column.Item().Text($"• {task.Description} - {task.StatusDisplay} - {task.PriorityDisplay}");
                            }
                        });
                        page.Footer().Text("صفحة 1");
                    });
                });

                document.GeneratePdf(filePath);
                return filePath;
            });
        }

        public async Task<string> GenerateSitesPdfReportAsync(IEnumerable<Site> sites, string filePath)
        {
            return await Task.Run(() =>
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma").FontSize(10));

                        page.Header().Text("تقرير المواقع").FontSize(20).Bold();
                        page.Content().Column(column =>
                        {
                            foreach (var site in sites)
                            {
                                column.Item().Text($"• {site.Name} - {site.Address ?? "غير محدد"} - {site.Network?.Name ?? "غير محدد"}");
                            }
                        });
                        page.Footer().Text("صفحة 1");
                    });
                });

                document.GeneratePdf(filePath);
                return filePath;
            });
        }

        public async Task<string> GenerateInventoryPdfReportAsync(IEnumerable<Inventory> inventory, string filePath)
        {
            return await Task.Run(() =>
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma").FontSize(10));

                        page.Header().Text("تقرير المخزون").FontSize(20).Bold();
                        page.Content().Column(column =>
                        {
                            foreach (var item in inventory)
                            {
                                column.Item().Text($"• {item.Name} - الكمية: {item.Quantity} - الفئة: {item.Category ?? "غير محدد"}");
                            }
                        });
                        page.Footer().Text("صفحة 1");
                    });
                });

                document.GeneratePdf(filePath);
                return filePath;
            });
        }

        public async Task<string> GeneratePurchasesPdfReportAsync(IEnumerable<Purchase> purchases, string filePath)
        {
            return await Task.Run(() =>
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma").FontSize(10));

                        page.Header().Text("تقرير المشتريات").FontSize(20).Bold();
                        page.Content().Column(column =>
                        {
                            foreach (var purchase in purchases)
                            {
                                column.Item().Text($"• {purchase.ItemType} - الكمية: {purchase.Quantity} - السعر: {purchase.Price} - التاريخ: {purchase.Date:yyyy/MM/dd}");
                            }
                        });
                        page.Footer().Text("صفحة 1");
                    });
                });

                document.GeneratePdf(filePath);
                return filePath;
            });
        }

        public async Task<string> GenerateStatisticsPdfReportAsync(string filePath)
        {
            return await Task.Run(async () =>
            {
                var devices = await _deviceService.GetAllAsync();
                var users = await _userService.GetAllAsync();
                var tasks = await _taskService.GetAllAsync();

                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma").FontSize(12));

                        page.Header().Text("تقرير الإحصائيات العامة").FontSize(20).Bold();
                        page.Content().Column(column =>
                        {
                            column.Item().Text("إحصائيات الأجهزة").FontSize(16).Bold();
                            column.Item().Text($"إجمالي الأجهزة: {devices.Count()}");
                            column.Item().Text($"الأجهزة النشطة: {devices.Count(d => d.Status == "active")}");
                            column.Item().Text($"الأجهزة غير النشطة: {devices.Count(d => d.Status == "inactive")}");

                            column.Item().Text("إحصائيات المستخدمين").FontSize(16).Bold();
                            column.Item().Text($"إجمالي المستخدمين: {users.Count()}");
                            column.Item().Text($"المستخدمين النشطين: {users.Count(u => u.IsActive)}");

                            column.Item().Text("إحصائيات المهام").FontSize(16).Bold();
                            column.Item().Text($"إجمالي المهام: {tasks.Count()}");
                            column.Item().Text($"المهام المعلقة: {tasks.Count(t => t.Status == "pending")}");
                            column.Item().Text($"المهام المكتملة: {tasks.Count(t => t.Status == "completed")}");
                        });
                        page.Footer().Text("صفحة 1");
                    });
                });

                document.GeneratePdf(filePath);
                return filePath;
            });
        }

        #endregion

        #region Print Reports

        public async Task PrintDevicesReportAsync(IEnumerable<Device> devices)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreateDevicePrintDocument(devices);
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "تقرير الأجهزة");
                }
            });
        }

        public async Task PrintUsersReportAsync(IEnumerable<User> users)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreateUserPrintDocument(users);
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "تقرير المستخدمين");
                }
            });
        }

        public async Task PrintTasksReportAsync(IEnumerable<NetworkManagement.Models.Task> tasks)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreateTaskPrintDocument(tasks);
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "تقرير المهام");
                }
            });
        }

        public async Task PrintSitesReportAsync(IEnumerable<Site> sites)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreateSitePrintDocument(sites);
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "تقرير المواقع");
                }
            });
        }

        public async Task PrintInventoryReportAsync(IEnumerable<Inventory> inventory)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreateInventoryPrintDocument(inventory);
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "تقرير المخزون");
                }
            });
        }

        public async Task PrintPurchasesReportAsync(IEnumerable<Purchase> purchases)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreatePurchasePrintDocument(purchases);
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "تقرير المشتريات");
                }
            });
        }

        public async Task PrintStatisticsReportAsync()
        {
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = await CreateStatisticsPrintDocument();
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "تقرير الإحصائيات");
                }
            });
        }

        #endregion

        #region Preview Reports

        public async Task PreviewDevicesReportAsync(IEnumerable<Device> devices)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var document = CreateDevicePrintDocument(devices);
                ShowPrintPreview(document, "معاينة تقرير الأجهزة");
            });
        }

        public async Task PreviewUsersReportAsync(IEnumerable<User> users)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var document = CreateUserPrintDocument(users);
                ShowPrintPreview(document, "معاينة تقرير المستخدمين");
            });
        }

        public async Task PreviewTasksReportAsync(IEnumerable<NetworkManagement.Models.Task> tasks)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var document = CreateTaskPrintDocument(tasks);
                ShowPrintPreview(document, "معاينة تقرير المهام");
            });
        }

        public async Task PreviewSitesReportAsync(IEnumerable<Site> sites)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var document = CreateSitePrintDocument(sites);
                ShowPrintPreview(document, "معاينة تقرير المواقع");
            });
        }

        public async Task PreviewInventoryReportAsync(IEnumerable<Inventory> inventory)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var document = CreateInventoryPrintDocument(inventory);
                ShowPrintPreview(document, "معاينة تقرير المخزون");
            });
        }

        public async Task PreviewPurchasesReportAsync(IEnumerable<Purchase> purchases)
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var document = CreatePurchasePrintDocument(purchases);
                ShowPrintPreview(document, "معاينة تقرير المشتريات");
            });
        }

        public async Task PreviewStatisticsReportAsync()
        {
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                var document = await CreateStatisticsPrintDocument();
                ShowPrintPreview(document, "معاينة تقرير الإحصائيات");
            });
        }

        #endregion

        #region Utility Methods

        public async Task<string> GetSaveFilePathAsync(string defaultFileName, string filter)
        {
            return await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var saveFileDialog = new SaveFileDialog
                {
                    FileName = defaultFileName,
                    Filter = filter,
                    DefaultExt = ".xlsx"
                };

                return saveFileDialog.ShowDialog() == true ? saveFileDialog.FileName : string.Empty;
            });
        }

        public async Task<bool> OpenFileAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                    return true;
                }
                catch
                {
                    return false;
                }
            });
        }

        #endregion

        #region Print Document Creation

        private FlowDocument CreateDevicePrintDocument(IEnumerable<Device> devices)
        {
            var document = new FlowDocument();
            document.FlowDirection = FlowDirection.RightToLeft;

            // عنوان التقرير
            var title = new Paragraph(new Run("تقرير الأجهزة"))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            // معلومات التقرير
            var info = new Paragraph(new Run($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm} | المستخدم: {_authService.CurrentUser?.Name ?? "غير محدد"}"))
            {
                FontSize = 12,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(info);

            // جدول الأجهزة
            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);

            // تعريف الأعمدة
            table.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });
            table.Columns.Add(new TableColumn { Width = new GridLength(1.5, GridUnitType.Star) });
            table.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });
            table.Columns.Add(new TableColumn { Width = new GridLength(1.5, GridUnitType.Star) });
            table.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            // رأس الجدول
            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;

            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("المسؤول"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("النوع"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("الموقع"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("عنوان IP"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("الحالة"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });

            headerRowGroup.Rows.Add(headerRow);
            table.RowGroups.Add(headerRowGroup);

            // بيانات الجدول
            var dataRowGroup = new TableRowGroup();
            foreach (var device in devices)
            {
                var row = new TableRow();
                row.Cells.Add(new TableCell(new Paragraph(new Run(device.Responsible ?? "غير محدد"))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(device.Type ?? "غير محدد"))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(device.Location ?? "غير محدد"))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(device.Ip ?? "غير محدد"))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(device.StatusDisplay))) { Padding = new Thickness(5) });

                dataRowGroup.Rows.Add(row);
            }
            table.RowGroups.Add(dataRowGroup);

            document.Blocks.Add(table);
            return document;
        }

        private FlowDocument CreateUserPrintDocument(IEnumerable<User> users)
        {
            var document = new FlowDocument();
            document.FlowDirection = FlowDirection.RightToLeft;

            var title = new Paragraph(new Run("تقرير المستخدمين"))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);

            table.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });
            table.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });
            table.Columns.Add(new TableColumn { Width = new GridLength(1.5, GridUnitType.Star) });
            table.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });

            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;

            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("اسم المستخدم"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("الاسم الكامل"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("الدور"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("الشبكة"))) { FontWeight = FontWeights.Bold, Padding = new Thickness(5) });

            headerRowGroup.Rows.Add(headerRow);
            table.RowGroups.Add(headerRowGroup);

            var dataRowGroup = new TableRowGroup();
            foreach (var user in users)
            {
                var row = new TableRow();
                row.Cells.Add(new TableCell(new Paragraph(new Run(user.Username))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(user.Name))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(user.RoleDisplay))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(user.NetworkName))) { Padding = new Thickness(5) });

                dataRowGroup.Rows.Add(row);
            }
            table.RowGroups.Add(dataRowGroup);

            document.Blocks.Add(table);
            return document;
        }

        // دوال مبسطة لباقي أنواع التقارير
        private FlowDocument CreateTaskPrintDocument(IEnumerable<NetworkManagement.Models.Task> tasks)
        {
            var document = new FlowDocument();
            document.FlowDirection = FlowDirection.RightToLeft;

            var title = new Paragraph(new Run("تقرير المهام"))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            foreach (var task in tasks.Take(20)) // تحديد عدد المهام للطباعة
            {
                var taskParagraph = new Paragraph();
                taskParagraph.Inlines.Add(new Run($"• {task.Description} - {task.StatusDisplay} - {task.PriorityDisplay}"));
                document.Blocks.Add(taskParagraph);
            }

            return document;
        }

        private FlowDocument CreateSitePrintDocument(IEnumerable<Site> sites)
        {
            var document = new FlowDocument();
            document.FlowDirection = FlowDirection.RightToLeft;

            var title = new Paragraph(new Run("تقرير المواقع"))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            foreach (var site in sites)
            {
                var siteParagraph = new Paragraph();
                siteParagraph.Inlines.Add(new Run($"• {site.Name} - {site.Address ?? "غير محدد"} - {site.Network?.Name ?? "غير محدد"}"));
                document.Blocks.Add(siteParagraph);
            }

            return document;
        }

        private FlowDocument CreateInventoryPrintDocument(IEnumerable<Inventory> inventory)
        {
            var document = new FlowDocument();
            document.FlowDirection = FlowDirection.RightToLeft;

            var title = new Paragraph(new Run("تقرير المخزون"))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            foreach (var item in inventory)
            {
                var itemParagraph = new Paragraph();
                itemParagraph.Inlines.Add(new Run($"• {item.Name} - الكمية: {item.Quantity} - الفئة: {item.Category ?? "غير محدد"}"));
                document.Blocks.Add(itemParagraph);
            }

            return document;
        }

        private FlowDocument CreatePurchasePrintDocument(IEnumerable<Purchase> purchases)
        {
            var document = new FlowDocument();
            document.FlowDirection = FlowDirection.RightToLeft;

            var title = new Paragraph(new Run("تقرير المشتريات"))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            foreach (var purchase in purchases)
            {
                var purchaseParagraph = new Paragraph();
                purchaseParagraph.Inlines.Add(new Run($"• {purchase.ItemType} - الكمية: {purchase.Quantity} - السعر: {purchase.Price:C} - التاريخ: {purchase.Date:yyyy/MM/dd}"));
                document.Blocks.Add(purchaseParagraph);
            }

            return document;
        }

        private async Task<FlowDocument> CreateStatisticsPrintDocument()
        {
            var document = new FlowDocument();
            document.FlowDirection = FlowDirection.RightToLeft;

            var title = new Paragraph(new Run("تقرير الإحصائيات العامة"))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            // جمع البيانات
            var devices = await _deviceService.GetAllAsync();
            var users = await _userService.GetAllAsync();
            var tasks = await _taskService.GetAllAsync();

            var deviceList = devices.ToList();
            var userList = users.ToList();
            var taskList = tasks.ToList();

            // إحصائيات الأجهزة
            var devicesSection = new Paragraph();
            devicesSection.Inlines.Add(new Run("إحصائيات الأجهزة:") { FontWeight = FontWeights.Bold });
            devicesSection.Inlines.Add(new LineBreak());
            devicesSection.Inlines.Add(new Run($"إجمالي الأجهزة: {deviceList.Count}"));
            devicesSection.Inlines.Add(new LineBreak());
            devicesSection.Inlines.Add(new Run($"الأجهزة النشطة: {deviceList.Count(d => d.Status == "active")}"));
            devicesSection.Inlines.Add(new LineBreak());
            devicesSection.Inlines.Add(new Run($"الأجهزة غير النشطة: {deviceList.Count(d => d.Status == "inactive")}"));
            document.Blocks.Add(devicesSection);

            // إحصائيات المستخدمين
            var usersSection = new Paragraph();
            usersSection.Inlines.Add(new Run("إحصائيات المستخدمين:") { FontWeight = FontWeights.Bold });
            usersSection.Inlines.Add(new LineBreak());
            usersSection.Inlines.Add(new Run($"إجمالي المستخدمين: {userList.Count}"));
            usersSection.Inlines.Add(new LineBreak());
            usersSection.Inlines.Add(new Run($"المستخدمين النشطين: {userList.Count(u => u.IsActive)}"));
            document.Blocks.Add(usersSection);

            // إحصائيات المهام
            var tasksSection = new Paragraph();
            tasksSection.Inlines.Add(new Run("إحصائيات المهام:") { FontWeight = FontWeights.Bold });
            tasksSection.Inlines.Add(new LineBreak());
            tasksSection.Inlines.Add(new Run($"إجمالي المهام: {taskList.Count}"));
            tasksSection.Inlines.Add(new LineBreak());
            tasksSection.Inlines.Add(new Run($"المهام المعلقة: {taskList.Count(t => t.Status == "pending")}"));
            tasksSection.Inlines.Add(new LineBreak());
            tasksSection.Inlines.Add(new Run($"المهام المكتملة: {taskList.Count(t => t.Status == "completed")}"));
            document.Blocks.Add(tasksSection);

            return document;
        }

        private void ShowPrintPreview(FlowDocument document, string title)
        {
            // تحويل FlowDocument إلى FixedDocument للمعاينة
            var fixedDocument = ConvertFlowDocumentToFixedDocument(document);

            var previewWindow = new Window
            {
                Title = title,
                Width = 800,
                Height = 600,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Content = new DocumentViewer
                {
                    Document = fixedDocument
                }
            };
            previewWindow.Show();
        }

        private FixedDocument ConvertFlowDocumentToFixedDocument(FlowDocument flowDocument)
        {
            var fixedDocument = new FixedDocument();
            var pageContent = new PageContent();
            var fixedPage = new FixedPage();

            // تعيين حجم الصفحة
            fixedPage.Width = 793.7; // A4 width in pixels (96 DPI)
            fixedPage.Height = 1122.5; // A4 height in pixels (96 DPI)

            // إنشاء FlowDocumentScrollViewer لعرض FlowDocument
            var flowDocumentReader = new FlowDocumentScrollViewer
            {
                Document = flowDocument,
                Width = fixedPage.Width - 40, // هامش 20 من كل جانب
                Height = fixedPage.Height - 40,
                VerticalScrollBarVisibility = ScrollBarVisibility.Hidden,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Hidden
            };

            // وضع FlowDocumentReader في الصفحة
            Canvas.SetLeft(flowDocumentReader, 20);
            Canvas.SetTop(flowDocumentReader, 20);
            fixedPage.Children.Add(flowDocumentReader);

            pageContent.Child = fixedPage;
            fixedDocument.Pages.Add(pageContent);

            return fixedDocument;
        }

        #endregion

        }
    }