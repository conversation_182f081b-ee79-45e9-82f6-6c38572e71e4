using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Diagnostics;
using MySqlConnector;
using System.Text;

namespace NetworkManagement.Services
{
    public class DatabaseService : IDatabaseService
    {
        private readonly ISettingsService _settingsService;

        public DatabaseService(ISettingsService settingsService)
        {
            _settingsService = settingsService;
        }

        public async Task<bool> CreateDatabaseAsync()
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                
                // First, connect without specifying database to create it
                var connectionStringWithoutDb = BuildConnectionString(false);

                using var connection = new MySqlConnection(connectionStringWithoutDb);
                await connection.OpenAsync();

                // Create database if not exists
                using var createDbCommand = connection.CreateCommand();
                createDbCommand.CommandText = $"CREATE DATABASE IF NOT EXISTS `{settings.MySQLDatabase}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;";
                await createDbCommand.ExecuteNonQueryAsync();

                // Switch to the new database
                using var useDbCommand = connection.CreateCommand();
                useDbCommand.CommandText = $"USE `{settings.MySQLDatabase}`;";
                await useDbCommand.ExecuteNonQueryAsync();

                // Create tables
                var createTablesSuccess = await CreateTablesAsync();
                
                return createTablesSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating database: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> CreateTablesAsync()
        {
            MySqlConnection? connection = null;
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 Starting database tables creation/update process...");

                var connectionString = BuildConnectionString(true);
                connection = new MySqlConnection(connectionString);

                // اختبار الاتصال أولاً
                await connection.OpenAsync();
                System.Diagnostics.Debug.WriteLine("✅ Database connection established");

                // التحقق من إصدار MySQL
                await ValidateMySqlVersionAsync(connection);

                // إنشاء الجداول الأساسية
                await CreateBasicTablesAsync(connection);

                // تحديث بنية الجداول الموجودة
                await UpdateTablesStructureAsync(connection);

                // جعل NetworkId اختياري في جميع الجداول
                await MakeNetworkIdOptionalAsync(connection);

                System.Diagnostics.Debug.WriteLine("🎉 Database tables creation/update completed successfully");
                return true;
            }
            catch (MySqlException ex)
            {
                var errorMessage = ex.Number switch
                {
                    1045 => "خطأ في اسم المستخدم أو كلمة المرور لقاعدة البيانات",
                    1049 => "قاعدة البيانات المحددة غير موجودة",
                    2003 => "لا يمكن الاتصال بخادم MySQL",
                    1146 => "جدول مطلوب غير موجود",
                    1054 => "عمود مطلوب غير موجود",
                    _ => $"خطأ MySQL: {ex.Message}"
                };

                System.Diagnostics.Debug.WriteLine($"❌ MySQL Error ({ex.Number}): {errorMessage}");
                return false;
            }
            catch (FileNotFoundException ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ SQL file not found: {ex.Message}");
                // محاولة إنشاء الجداول باستخدام SQL المدمج
                try
                {
                    if (connection?.State == System.Data.ConnectionState.Open)
                    {
                        await CreateTablesWithEmbeddedSqlAsync(connection);
                        return true;
                    }
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Fallback creation failed: {fallbackEx.Message}");
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Unexpected error creating tables: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
            finally
            {
                connection?.Dispose();
            }
        }

        private async Task ValidateMySqlVersionAsync(MySqlConnection connection)
        {
            try
            {
                using var command = connection.CreateCommand();
                command.CommandText = "SELECT VERSION()";
                var versionResult = await command.ExecuteScalarAsync();
                var version = versionResult?.ToString();
                System.Diagnostics.Debug.WriteLine($"ℹ️ MySQL Version: {version}");

                // التحقق من الحد الأدنى للإصدار (5.7+)
                if (!string.IsNullOrEmpty(version))
                {
                    var versionParts = version.Split('.');
                    if (versionParts.Length >= 2 &&
                        int.TryParse(versionParts[0], out var major) &&
                        int.TryParse(versionParts[1], out var minor))
                    {
                        if (major < 5 || (major == 5 && minor < 7))
                        {
                            System.Diagnostics.Debug.WriteLine("⚠️ Warning: MySQL version is older than 5.7, some features may not work properly");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ Could not validate MySQL version: {ex.Message}");
            }
        }

        private async Task CreateBasicTablesAsync(MySqlConnection connection)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 Creating/updating basic database tables...");

                // قراءة ملف SQL للإنشاء
                var sqlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Database", "CreateDatabase.sql");

                if (File.Exists(sqlFilePath))
                {
                    System.Diagnostics.Debug.WriteLine($"📄 Reading SQL file: {sqlFilePath}");
                    var sqlContent = await File.ReadAllTextAsync(sqlFilePath, Encoding.UTF8);
                    await ExecuteSqlBatchAsync(connection, sqlContent);
                    System.Diagnostics.Debug.WriteLine("✅ SQL file executed successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ SQL file not found, using embedded SQL");
                    await CreateTablesWithEmbeddedSqlAsync(connection);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error creating basic tables: {ex.Message}");
                throw; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
            }
        }

        private async Task UpdateTablesStructureAsync(MySqlConnection connection)
        {
            try
            {
                // إنشاء جدول لتتبع إصدارات المخطط
                await CreateSchemaVersionTableAsync(connection);

                // الحصول على الإصدار الحالي
                var currentVersion = await GetCurrentSchemaVersionAsync(connection);

                // تطبيق التحديثات المطلوبة
                await ApplySchemaUpdatesAsync(connection, currentVersion);

                // Check and add missing columns to Devices table
                var columnsToAdd = new[]
                {
                    new { Name = "Phone", Definition = "VARCHAR(20)", After = "Location" },
                    new { Name = "PowerConnection", Definition = "VARCHAR(100)", After = "LastCheck" },
                    new { Name = "AdapterType", Definition = "VARCHAR(50)", After = "PowerConnection" },
                    new { Name = "BroadcastNetworkName", Definition = "VARCHAR(100)", After = "LinkedNetwork" }
                };

                foreach (var column in columnsToAdd)
                {
                    try
                    {
                        // Check if column exists
                        using var checkCommand = connection.CreateCommand();
                        checkCommand.CommandText = @"
                            SELECT COUNT(*)
                            FROM information_schema.columns
                            WHERE table_schema = DATABASE()
                            AND table_name = 'Devices'
                            AND column_name = @columnName";
                        checkCommand.Parameters.AddWithValue("@columnName", column.Name);

                        var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

                        if (!exists)
                        {
                            // Add the column with transaction support
                            using var transaction = await connection.BeginTransactionAsync();
                            try
                            {
                                using var alterCommand = connection.CreateCommand();
                                alterCommand.Transaction = transaction;
                                alterCommand.CommandText = $"ALTER TABLE Devices ADD COLUMN {column.Name} {column.Definition} AFTER {column.After}";
                                await alterCommand.ExecuteNonQueryAsync();

                                await transaction.CommitAsync();
                                System.Diagnostics.Debug.WriteLine($"✅ Added column: {column.Name}");
                            }
                            catch (Exception)
                            {
                                await transaction.RollbackAsync();
                                throw;
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"ℹ️ Column {column.Name} already exists");
                        }
                    }
                    catch (MySqlException ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ Error adding column {column.Name}: {ex.Message}");
                        // لا نتوقف عند فشل عمود واحد، نكمل مع الباقي
                    }
                }

                // تحديث إصدار المخطط
                await UpdateSchemaVersionAsync(connection, "1.2.0", "Added missing columns to Devices table");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating tables structure: {ex.Message}");
                throw; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
            }
        }

        private async Task CreateSchemaVersionTableAsync(MySqlConnection connection)
        {
            try
            {
                using var command = connection.CreateCommand();
                command.CommandText = @"
                    CREATE TABLE IF NOT EXISTS SchemaVersions (
                        Id INT AUTO_INCREMENT PRIMARY KEY,
                        Version VARCHAR(20) NOT NULL,
                        Description VARCHAR(500),
                        AppliedAt DATETIME DEFAULT NOW(),
                        UNIQUE KEY unique_version (Version)
                    )";
                await command.ExecuteNonQueryAsync();

                // إدراج الإصدار الأولي إذا لم يكن موجود
                using var insertCommand = connection.CreateCommand();
                insertCommand.CommandText = @"
                    INSERT IGNORE INTO SchemaVersions (Version, Description)
                    VALUES ('1.0.0', 'Initial database schema')";
                await insertCommand.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error creating schema version table: {ex.Message}");
            }
        }

        private async Task<string> GetCurrentSchemaVersionAsync(MySqlConnection connection)
        {
            try
            {
                using var command = connection.CreateCommand();
                command.CommandText = "SELECT Version FROM SchemaVersions ORDER BY AppliedAt DESC LIMIT 1";
                var result = await command.ExecuteScalarAsync();
                return result?.ToString() ?? "1.0.0";
            }
            catch (Exception)
            {
                return "1.0.0"; // الإصدار الافتراضي
            }
        }

        private async Task UpdateSchemaVersionAsync(MySqlConnection connection, string version, string description)
        {
            try
            {
                using var command = connection.CreateCommand();
                command.CommandText = @"
                    INSERT IGNORE INTO SchemaVersions (Version, Description)
                    VALUES (@version, @description)";
                command.Parameters.AddWithValue("@version", version);
                command.Parameters.AddWithValue("@description", description);
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating schema version: {ex.Message}");
            }
        }

        private async Task ApplySchemaUpdatesAsync(MySqlConnection connection, string currentVersion)
        {
            try
            {
                // تطبيق التحديثات بناءً على الإصدار الحالي
                var updates = new Dictionary<string, Func<MySqlConnection, Task>>
                {
                    ["1.1.0"] = async (conn) => await ApplyUpdate_1_1_0(conn),
                    ["1.2.0"] = async (conn) => await ApplyUpdate_1_2_0(conn)
                };

                foreach (var update in updates)
                {
                    if (string.Compare(currentVersion, update.Key, StringComparison.OrdinalIgnoreCase) < 0)
                    {
                        try
                        {
                            await update.Value(connection);
                            System.Diagnostics.Debug.WriteLine($"✅ Applied update: {update.Key}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ Failed to apply update {update.Key}: {ex.Message}");
                            throw; // إيقاف التحديثات عند الفشل
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error applying schema updates: {ex.Message}");
                throw;
            }
        }

        private async Task ApplyUpdate_1_1_0(MySqlConnection connection)
        {
            // تحديثات الإصدار 1.1.0
            var statements = new[]
            {
                "ALTER TABLE Devices MODIFY COLUMN NetworkId VARCHAR(50) NULL",
                "ALTER TABLE Devices MODIFY COLUMN SiteId VARCHAR(50) NULL"
            };

            foreach (var statement in statements)
            {
                try
                {
                    using var command = connection.CreateCommand();
                    command.CommandText = statement;
                    await command.ExecuteNonQueryAsync();
                }
                catch (MySqlException ex) when (ex.Number == 1060) // Duplicate column
                {
                    // تجاهل إذا كان العمود موجود بالفعل
                }
            }
        }

        private async Task ApplyUpdate_1_2_0(MySqlConnection connection)
        {
            // تحديثات الإصدار 1.2.0
            var statements = new[]
            {
                "ALTER TABLE Sites MODIFY COLUMN NetworkId VARCHAR(50) NULL",
                "ALTER TABLE Tasks MODIFY COLUMN NetworkId VARCHAR(50) NULL",
                "ALTER TABLE Purchases MODIFY COLUMN NetworkId VARCHAR(50) NULL",
                "ALTER TABLE Inventory MODIFY COLUMN NetworkId VARCHAR(50) NULL"
            };

            foreach (var statement in statements)
            {
                try
                {
                    using var command = connection.CreateCommand();
                    command.CommandText = statement;
                    await command.ExecuteNonQueryAsync();
                }
                catch (MySqlException ex) when (ex.Number == 1060) // Duplicate column
                {
                    // تجاهل إذا كان العمود موجود بالفعل
                }
            }
        }

        private async Task MakeNetworkIdOptionalAsync(MySqlConnection connection)
        {
            try
            {
                // Make NetworkId and SiteId optional in all tables for flexibility
                var alterStatements = new Dictionary<string, string>
                {
                    ["Devices.NetworkId"] = "ALTER TABLE Devices MODIFY COLUMN NetworkId VARCHAR(50) NULL",
                    ["Devices.SiteId"] = "ALTER TABLE Devices MODIFY COLUMN SiteId VARCHAR(50) NULL",
                    ["Sites.NetworkId"] = "ALTER TABLE Sites MODIFY COLUMN NetworkId VARCHAR(50) NULL",
                    ["Tasks.NetworkId"] = "ALTER TABLE Tasks MODIFY COLUMN NetworkId VARCHAR(50) NULL",
                    ["Purchases.NetworkId"] = "ALTER TABLE Purchases MODIFY COLUMN NetworkId VARCHAR(50) NULL",
                    ["Inventory.NetworkId"] = "ALTER TABLE Inventory MODIFY COLUMN NetworkId VARCHAR(50) NULL"
                };

                int successCount = 0;
                int skipCount = 0;
                int errorCount = 0;

                foreach (var kvp in alterStatements)
                {
                    try
                    {
                        // التحقق من حالة العمود الحالية
                        var tableName = kvp.Key.Split('.')[0];
                        var columnName = kvp.Key.Split('.')[1];

                        using var checkCommand = connection.CreateCommand();
                        checkCommand.CommandText = @"
                            SELECT IS_NULLABLE
                            FROM information_schema.columns
                            WHERE table_schema = DATABASE()
                            AND table_name = @tableName
                            AND column_name = @columnName";
                        checkCommand.Parameters.AddWithValue("@tableName", tableName);
                        checkCommand.Parameters.AddWithValue("@columnName", columnName);

                        var nullableResult = await checkCommand.ExecuteScalarAsync();
                        var isNullable = nullableResult?.ToString();

                        if (isNullable == "YES")
                        {
                            System.Diagnostics.Debug.WriteLine($"ℹ️ Column {kvp.Key} is already nullable");
                            skipCount++;
                            continue;
                        }

                        // تطبيق التحديث مع Transaction
                        using var transaction = await connection.BeginTransactionAsync();
                        try
                        {
                            using var command = connection.CreateCommand();
                            command.Transaction = transaction;
                            command.CommandText = kvp.Value;
                            await command.ExecuteNonQueryAsync();

                            await transaction.CommitAsync();
                            System.Diagnostics.Debug.WriteLine($"✅ Made {kvp.Key} nullable");
                            successCount++;
                        }
                        catch (Exception)
                        {
                            await transaction.RollbackAsync();
                            throw;
                        }
                    }
                    catch (MySqlException ex) when (ex.Number == 1146) // Table doesn't exist
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ Table {kvp.Key.Split('.')[0]} doesn't exist, skipping");
                        skipCount++;
                    }
                    catch (MySqlException ex) when (ex.Number == 1054) // Column doesn't exist
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ Column {kvp.Key} doesn't exist, skipping");
                        skipCount++;
                    }
                    catch (MySqlException ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ MySQL error for {kvp.Key}: {ex.Message}");
                        errorCount++;
                        // لا نتوقف، نكمل مع الأعمدة الأخرى
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ Unexpected error for {kvp.Key}: {ex.Message}");
                        errorCount++;
                    }
                }

                // تقرير النتائج
                System.Diagnostics.Debug.WriteLine($"📊 MakeNetworkIdOptional Summary: ✅{successCount} ℹ️{skipCount} ❌{errorCount}");

                if (errorCount > 0 && successCount == 0)
                {
                    throw new InvalidOperationException($"Failed to make any columns nullable. Errors: {errorCount}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Critical error making NetworkId optional: {ex.Message}");
                throw; // إعادة رمي الخطأ الحرج
            }
        }

        public async Task<bool> ImportDatabaseAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                var settings = _settingsService.LoadSettings();
                
                // Use mysqldump for import if available, otherwise use direct SQL execution
                var mysqlPath = FindMySqlPath();
                if (!string.IsNullOrEmpty(mysqlPath))
                {
                    return await ImportWithMySqlCommandAsync(filePath, settings, mysqlPath);
                }
                else
                {
                    return await ImportWithDirectSqlAsync(filePath, settings);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error importing database: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> BackupDatabaseAsync(string filePath)
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                
                // Use mysqldump if available
                var mysqldumpPath = FindMySqlDumpPath();
                if (!string.IsNullOrEmpty(mysqldumpPath))
                {
                    return await BackupWithMySqlDumpAsync(filePath, settings, mysqldumpPath);
                }
                else
                {
                    return await BackupWithDirectSqlAsync(filePath, settings);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error backing up database: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> RestoreDatabaseAsync(string filePath)
        {
            try
            {
                // First drop and recreate database
                var settings = _settingsService.LoadSettings();
                var connectionStringWithoutDb = $"Server={settings.MySQLServer};User={settings.MySQLUser};Password={settings.MySQLPassword};";
                
                using var connection = new MySqlConnection(connectionStringWithoutDb);
                await connection.OpenAsync();

                // Drop database
                using var dropCommand = connection.CreateCommand();
                dropCommand.CommandText = $"DROP DATABASE IF EXISTS `{settings.MySQLDatabase}`;";
                await dropCommand.ExecuteNonQueryAsync();

                // Create database
                using var createCommand = connection.CreateCommand();
                createCommand.CommandText = $"CREATE DATABASE `{settings.MySQLDatabase}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;";
                await createCommand.ExecuteNonQueryAsync();

                // Import the backup
                return await ImportDatabaseAsync(filePath);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error restoring database: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DatabaseExistsAsync()
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                var connectionStringWithoutDb = $"Server={settings.MySQLServer};User={settings.MySQLUser};Password={settings.MySQLPassword};";
                
                using var connection = new MySqlConnection(connectionStringWithoutDb);
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = "SELECT COUNT(*) FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = @dbName";
                command.Parameters.AddWithValue("@dbName", settings.MySQLDatabase);
                
                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking database existence: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> TablesExistAsync()
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword};";
                
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = @dbName";
                command.Parameters.AddWithValue("@dbName", settings.MySQLDatabase);
                
                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking tables existence: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ExecuteSqlFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                var settings = _settingsService.LoadSettings();
                var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword};";
                
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                var sqlContent = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                await ExecuteSqlBatchAsync(connection, sqlContent);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error executing SQL file: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ExecuteSqlCommandAsync(string sqlCommand)
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword};";
                
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = sqlCommand;
                await command.ExecuteNonQueryAsync();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error executing SQL command: {ex.Message}");
                return false;
            }
        }

        public async Task<DatabaseInfo> GetDatabaseInfoAsync()
        {
            var info = new DatabaseInfo();
            
            try
            {
                var settings = _settingsService.LoadSettings();
                info.DatabaseName = settings.MySQLDatabase;
                
                var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword};";
                
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                info.Exists = true;

                // Get table count
                using var tableCountCommand = connection.CreateCommand();
                tableCountCommand.CommandText = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = @dbName";
                tableCountCommand.Parameters.AddWithValue("@dbName", settings.MySQLDatabase);
                info.TableCount = Convert.ToInt32(await tableCountCommand.ExecuteScalarAsync());

                // Get database size
                using var sizeCommand = connection.CreateCommand();
                sizeCommand.CommandText = @"
                    SELECT ROUND(SUM(data_length + index_length), 0) as size_bytes
                    FROM information_schema.tables 
                    WHERE table_schema = @dbName";
                sizeCommand.Parameters.AddWithValue("@dbName", settings.MySQLDatabase);
                var sizeResult = await sizeCommand.ExecuteScalarAsync();
                if (sizeResult != DBNull.Value && sizeResult != null)
                {
                    info.SizeInBytes = Convert.ToInt64(sizeResult);
                    info.SizeDisplay = FormatFileSize(info.SizeInBytes);
                }

                // Get charset and collation
                using var charsetCommand = connection.CreateCommand();
                charsetCommand.CommandText = @"
                    SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
                    FROM information_schema.SCHEMATA 
                    WHERE SCHEMA_NAME = @dbName";
                charsetCommand.Parameters.AddWithValue("@dbName", settings.MySQLDatabase);
                using var reader = await charsetCommand.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    info.Charset = reader.GetString(0);
                    info.Collation = reader.GetString(1);
                }

                return info;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting database info: {ex.Message}");
                info.Exists = false;
                return info;
            }
        }

        // Helper methods
        private string BuildConnectionString(bool includeDatabase = true)
        {
            var settings = _settingsService.LoadSettings();
            var builder = new MySqlConnectionStringBuilder
            {
                Server = settings.MySQLServer,
                UserID = settings.MySQLUser,
                Password = settings.MySQLPassword,
                // تحسينات الأداء والأمان
                Pooling = true,
                MinimumPoolSize = 0,
                MaximumPoolSize = 100,
                ConnectionTimeout = 30,
                DefaultCommandTimeout = 60,
                // دعم Multiple Active Result Sets
                AllowUserVariables = true,
                UseAffectedRows = false,
                // تحسينات الشبكة
                Keepalive = 60,
                // تحسينات الأمان
                SslMode = MySqlSslMode.Preferred,
                // تحسينات الترميز
                CharacterSet = "utf8mb4"
            };

            if (includeDatabase)
            {
                builder.Database = settings.MySQLDatabase;
            }

            return builder.ConnectionString;
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private async Task ExecuteSqlBatchAsync(MySqlConnection connection, string sqlContent)
        {
            if (string.IsNullOrWhiteSpace(sqlContent))
            {
                System.Diagnostics.Debug.WriteLine("⚠️ SQL content is empty or null");
                return;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine("📝 Executing SQL batch...");

                // تنظيف وتقسيم محتوى SQL
                var statements = SplitSqlStatements(sqlContent);

                int successCount = 0;
                int skipCount = 0;
                int errorCount = 0;

                foreach (var statement in statements)
                {
                    var trimmedStatement = statement.Trim();

                    // تخطي العبارات الفارغة والتعليقات
                    if (string.IsNullOrEmpty(trimmedStatement) ||
                        trimmedStatement.StartsWith("--") ||
                        trimmedStatement.StartsWith("/*") ||
                        trimmedStatement.StartsWith("#"))
                    {
                        skipCount++;
                        continue;
                    }

                    try
                    {
                        using var transaction = await connection.BeginTransactionAsync();
                        try
                        {
                            using var command = connection.CreateCommand();
                            command.Transaction = transaction;
                            command.CommandText = trimmedStatement;
                            command.CommandTimeout = 60; // 60 ثانية timeout

                            var rowsAffected = await command.ExecuteNonQueryAsync();
                            await transaction.CommitAsync();

                            successCount++;
                            System.Diagnostics.Debug.WriteLine($"✅ Executed statement (affected {rowsAffected} rows)");
                        }
                        catch (Exception)
                        {
                            await transaction.RollbackAsync();
                            throw;
                        }
                    }
                    catch (MySqlException ex) when (ex.Number == 1050) // Table already exists
                    {
                        System.Diagnostics.Debug.WriteLine($"ℹ️ Table already exists, skipping: {ex.Message}");
                        skipCount++;
                    }
                    catch (MySqlException ex) when (ex.Number == 1061) // Duplicate key name
                    {
                        System.Diagnostics.Debug.WriteLine($"ℹ️ Duplicate key, skipping: {ex.Message}");
                        skipCount++;
                    }
                    catch (MySqlException ex) when (ex.Number == 1062) // Duplicate entry
                    {
                        System.Diagnostics.Debug.WriteLine($"ℹ️ Duplicate entry, skipping: {ex.Message}");
                        skipCount++;
                    }
                    catch (MySqlException ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ MySQL error executing statement: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"Statement: {trimmedStatement.Substring(0, Math.Min(100, trimmedStatement.Length))}...");
                        errorCount++;

                        // إيقاف التنفيذ عند الأخطاء الحرجة
                        if (IsCriticalMySqlError(ex.Number))
                        {
                            throw new InvalidOperationException($"Critical MySQL error: {ex.Message}", ex);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ Unexpected error executing statement: {ex.Message}");
                        errorCount++;
                        throw; // إيقاف عند الأخطاء غير المتوقعة
                    }
                }

                // تقرير النتائج النهائي
                System.Diagnostics.Debug.WriteLine($"📊 SQL Batch Summary: ✅{successCount} ℹ️{skipCount} ❌{errorCount}");

                if (successCount == 0 && errorCount > 0)
                {
                    throw new InvalidOperationException($"No SQL statements executed successfully. Errors: {errorCount}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Critical error in SQL batch execution: {ex.Message}");
                throw;
            }
        }

        private List<string> SplitSqlStatements(string sqlContent)
        {
            var statements = new List<string>();
            var currentStatement = new StringBuilder();
            var inString = false;
            var stringChar = '\0';
            var inComment = false;
            var inMultiLineComment = false;

            for (int i = 0; i < sqlContent.Length; i++)
            {
                var currentChar = sqlContent[i];
                var nextChar = i + 1 < sqlContent.Length ? sqlContent[i + 1] : '\0';

                // معالجة التعليقات متعددة الأسطر
                if (!inString && currentChar == '/' && nextChar == '*')
                {
                    inMultiLineComment = true;
                    i++; // تخطي الحرف التالي
                    continue;
                }

                if (inMultiLineComment && currentChar == '*' && nextChar == '/')
                {
                    inMultiLineComment = false;
                    i++; // تخطي الحرف التالي
                    continue;
                }

                if (inMultiLineComment)
                    continue;

                // معالجة تعليقات السطر الواحد
                if (!inString && currentChar == '-' && nextChar == '-')
                {
                    inComment = true;
                    continue;
                }

                if (inComment && currentChar == '\n')
                {
                    inComment = false;
                    currentStatement.AppendLine();
                    continue;
                }

                if (inComment)
                    continue;

                // معالجة النصوص
                if ((currentChar == '\'' || currentChar == '"') && !inString)
                {
                    inString = true;
                    stringChar = currentChar;
                }
                else if (currentChar == stringChar && inString)
                {
                    // التحقق من escape character
                    if (i > 0 && sqlContent[i - 1] != '\\')
                    {
                        inString = false;
                        stringChar = '\0';
                    }
                }

                // معالجة الفاصلة المنقوطة
                if (!inString && currentChar == ';')
                {
                    var statement = currentStatement.ToString().Trim();
                    if (!string.IsNullOrEmpty(statement))
                    {
                        statements.Add(statement);
                    }
                    currentStatement.Clear();
                }
                else
                {
                    currentStatement.Append(currentChar);
                }
            }

            // إضافة العبارة الأخيرة إذا لم تنته بفاصلة منقوطة
            var lastStatement = currentStatement.ToString().Trim();
            if (!string.IsNullOrEmpty(lastStatement))
            {
                statements.Add(lastStatement);
            }

            return statements;
        }

        private bool IsCriticalMySqlError(int errorNumber)
        {
            // أرقام الأخطاء الحرجة التي تتطلب إيقاف التنفيذ
            var criticalErrors = new[]
            {
                1045, // Access denied
                1049, // Unknown database
                2003, // Can't connect to MySQL server
                1044, // Access denied for user to database
                1142, // Command denied to user
                1143, // Column access denied
                1370  // execute command denied
            };

            return Array.IndexOf(criticalErrors, errorNumber) >= 0;
        }

        private async Task CreateTablesWithEmbeddedSqlAsync(MySqlConnection connection)
        {
            var createTablesSql = @"
                -- جدول الشبكات
                CREATE TABLE IF NOT EXISTS Networks (
                    Id VARCHAR(50) PRIMARY KEY,
                    Name VARCHAR(100) NOT NULL,
                    Description VARCHAR(500),
                    Color VARCHAR(20) DEFAULT '#2196F3',
                    IsActive BOOLEAN DEFAULT 1,
                    CreatedAt DATETIME DEFAULT NOW(),
                    UpdatedAt DATETIME DEFAULT NOW(),
                    UNIQUE KEY unique_name (Name)
                );

                -- جدول المستخدمين
                CREATE TABLE IF NOT EXISTS Users (
                    Id VARCHAR(50) PRIMARY KEY,
                    Username VARCHAR(50) UNIQUE NOT NULL,
                    Password VARCHAR(255) NOT NULL,
                    Name VARCHAR(100) NOT NULL,
                    Email VARCHAR(100),
                    Role VARCHAR(50) NOT NULL,
                    NetworkId VARCHAR(50),
                    IsActive BOOLEAN DEFAULT 1,
                    CreatedAt DATETIME DEFAULT NOW(),
                    UpdatedAt DATETIME DEFAULT NOW(),
                    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
                    INDEX idx_username (Username),
                    INDEX idx_role (Role),
                    INDEX idx_network (NetworkId)
                );

                -- جدول المواقع (مطابق تماماً لـ Site.cs Model)
                CREATE TABLE IF NOT EXISTS Sites (
                    Id VARCHAR(50) PRIMARY KEY,
                    Name VARCHAR(100) NOT NULL,
                    Address VARCHAR(200),
                    Phone VARCHAR(20),
                    GpsLat DOUBLE,
                    GpsLng DOUBLE,
                    PowerSource VARCHAR(100),
                    StorageCapacity INT,
                    DailyConsumption INT,
                    InstallationBase VARCHAR(100),
                    Boxes INT,
                    WireLength INT,
                    AssociatedDeviceIds TEXT,
                    NetworkId VARCHAR(50),
                    CreatedAt DATETIME DEFAULT NOW(),
                    UpdatedAt DATETIME DEFAULT NOW(),
                    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
                    INDEX idx_name (Name),
                    INDEX idx_network (NetworkId)
                );

                -- جدول الأجهزة (مطابق تماماً لـ Device.cs Model)
                CREATE TABLE IF NOT EXISTS Devices (
                    Id VARCHAR(50) PRIMARY KEY,
                    Responsible VARCHAR(100),
                    Type VARCHAR(50),
                    Location VARCHAR(200),
                    Phone VARCHAR(20),
                    Ip VARCHAR(15),
                    InstallDate DATETIME,
                    PowerConnection VARCHAR(100),
                    AdapterType VARCHAR(50),
                    NetworkCableLength INT,
                    PowerCableLength INT,
                    ConnectionMethod VARCHAR(50),
                    LinkedNetwork VARCHAR(50),
                    BroadcastNetworkName VARCHAR(100),
                    Channel INT,
                    ConnectedDevices INT,
                    Status VARCHAR(20) DEFAULT 'active',
                    LastCheck DATETIME,
                    SiteId VARCHAR(50),
                    NetworkId VARCHAR(50),
                    CreatedAt DATETIME DEFAULT NOW(),
                    UpdatedAt DATETIME DEFAULT NOW(),
                    FOREIGN KEY (SiteId) REFERENCES Sites(Id) ON DELETE SET NULL,
                    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
                    INDEX idx_ip (Ip),
                    INDEX idx_status (Status),
                    INDEX idx_type (Type),
                    INDEX idx_site (SiteId),
                    INDEX idx_network (NetworkId)
                );

                -- جدول المهام (مطابق تماماً لـ Task.cs Model)
                CREATE TABLE IF NOT EXISTS Tasks (
                    Id VARCHAR(50) PRIMARY KEY,
                    UserId VARCHAR(50) NOT NULL,
                    Description VARCHAR(1000) NOT NULL,
                    Date DATETIME NOT NULL,
                    Status VARCHAR(50) DEFAULT 'pending',
                    Notes TEXT,
                    Priority VARCHAR(50) DEFAULT 'medium',
                    DueDate DATETIME,
                    CompletedAt DATETIME,
                    NetworkId VARCHAR(50),
                    CreatedAt DATETIME DEFAULT NOW(),
                    UpdatedAt DATETIME DEFAULT NOW(),
                    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
                    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
                    INDEX idx_user (UserId),
                    INDEX idx_status (Status),
                    INDEX idx_network (NetworkId),
                    INDEX idx_date (Date)
                );

                -- جدول المشتريات (مطابق تماماً لـ Purchase.cs Model)
                CREATE TABLE IF NOT EXISTS Purchases (
                    Id VARCHAR(50) PRIMARY KEY,
                    ItemType VARCHAR(100) NOT NULL,
                    Description VARCHAR(500),
                    Quantity INT DEFAULT 1,
                    Unit VARCHAR(50),
                    Price DECIMAL(10,2),
                    Supplier VARCHAR(200),
                    InvoiceNumber VARCHAR(100),
                    Category VARCHAR(100),
                    Date DATETIME DEFAULT NOW(),
                    NetworkId VARCHAR(50),
                    CreatedAt DATETIME DEFAULT NOW(),
                    UpdatedAt DATETIME DEFAULT NOW(),
                    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
                    INDEX idx_item_type (ItemType),
                    INDEX idx_category (Category),
                    INDEX idx_date (Date),
                    INDEX idx_network (NetworkId)
                );

                -- جدول المخزون (مطابق تماماً لـ Inventory.cs Model)
                CREATE TABLE IF NOT EXISTS Inventory (
                    Id VARCHAR(50) PRIMARY KEY,
                    Name VARCHAR(100) NOT NULL,
                    Description VARCHAR(500),
                    Quantity INT DEFAULT 0,
                    Unit VARCHAR(50),
                    UnitPrice DECIMAL(10,2),
                    Location VARCHAR(200),
                    Supplier VARCHAR(200),
                    MinimumStock INT,
                    MaximumStock INT,
                    Category VARCHAR(100),
                    LastUpdated DATETIME DEFAULT NOW(),
                    NetworkId VARCHAR(50),
                    CreatedAt DATETIME DEFAULT NOW(),
                    UpdatedAt DATETIME DEFAULT NOW(),
                    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
                    INDEX idx_name (Name),
                    INDEX idx_category (Category),
                    INDEX idx_network (NetworkId)
                );

                -- إدراج المستخدم المدير
                INSERT IGNORE INTO Users (Id, Username, Password, Name, Role, NetworkId)
                VALUES ('admin', 'admin', 'password', 'المدير العام', 'Admin', NULL);
            ";

            await ExecuteSqlBatchAsync(connection, createTablesSql);
        }

        private string FindMySqlPath()
        {
            var possiblePaths = new[]
            {
                @"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe",
                @"C:\Program Files\MySQL\MySQL Server 5.7\bin\mysql.exe",
                @"C:\MySQL\bin\mysql.exe",
                @"mysql.exe" // If in PATH
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path) || path == "mysql.exe")
                {
                    return path;
                }
            }

            return "";
        }

        private string FindMySqlDumpPath()
        {
            var possiblePaths = new[]
            {
                @"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe",
                @"C:\Program Files\MySQL\MySQL Server 5.7\bin\mysqldump.exe",
                @"C:\MySQL\bin\mysqldump.exe",
                @"mysqldump.exe" // If in PATH
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path) || path == "mysqldump.exe")
                {
                    return path;
                }
            }

            return "";
        }

        private async Task<bool> ImportWithMySqlCommandAsync(string filePath, AppSettings settings, string mysqlPath)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = mysqlPath,
                    Arguments = $"-h {settings.MySQLServer} -u {settings.MySQLUser} -p{settings.MySQLPassword} {settings.MySQLDatabase}",
                    RedirectStandardInput = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process != null)
                {
                    var sqlContent = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                    await process.StandardInput.WriteAsync(sqlContent);
                    process.StandardInput.Close();

                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error importing with mysql command: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> ImportWithDirectSqlAsync(string filePath, AppSettings settings)
        {
            try
            {
                var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword};";

                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                var sqlContent = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                await ExecuteSqlBatchAsync(connection, sqlContent);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error importing with direct SQL: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> BackupWithMySqlDumpAsync(string filePath, AppSettings settings, string mysqldumpPath)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = mysqldumpPath,
                    Arguments = $"-h {settings.MySQLServer} -u {settings.MySQLUser} -p{settings.MySQLPassword} --routines --triggers {settings.MySQLDatabase}",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process != null)
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    await process.WaitForExitAsync();

                    if (process.ExitCode == 0)
                    {
                        await File.WriteAllTextAsync(filePath, output, Encoding.UTF8);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error backing up with mysqldump: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> BackupWithDirectSqlAsync(string filePath, AppSettings settings)
        {
            try
            {
                var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword};";

                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                var sqlBuilder = new StringBuilder();

                // Add header
                sqlBuilder.AppendLine($"-- MySQL dump for database: {settings.MySQLDatabase}");
                sqlBuilder.AppendLine($"-- Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sqlBuilder.AppendLine();

                // Get all tables
                using var tablesCommand = connection.CreateCommand();
                tablesCommand.CommandText = "SHOW TABLES";
                using var tablesReader = await tablesCommand.ExecuteReaderAsync();

                var tables = new List<string>();
                while (await tablesReader.ReadAsync())
                {
                    tables.Add(tablesReader.GetString(0));
                }
                tablesReader.Close();

                // Export each table
                foreach (var table in tables)
                {
                    // Get CREATE TABLE statement
                    using var createCommand = connection.CreateCommand();
                    createCommand.CommandText = $"SHOW CREATE TABLE `{table}`";
                    using var createReader = await createCommand.ExecuteReaderAsync();

                    if (await createReader.ReadAsync())
                    {
                        sqlBuilder.AppendLine($"DROP TABLE IF EXISTS `{table}`;");
                        sqlBuilder.AppendLine(createReader.GetString(1) + ";");
                        sqlBuilder.AppendLine();
                    }
                    createReader.Close();

                    // Get table data
                    using var dataCommand = connection.CreateCommand();
                    dataCommand.CommandText = $"SELECT * FROM `{table}`";
                    using var dataReader = await dataCommand.ExecuteReaderAsync();

                    if (dataReader.HasRows)
                    {
                        var columns = new List<string>();
                        for (int i = 0; i < dataReader.FieldCount; i++)
                        {
                            columns.Add($"`{dataReader.GetName(i)}`");
                        }

                        sqlBuilder.AppendLine($"INSERT INTO `{table}` ({string.Join(", ", columns)}) VALUES");

                        var values = new List<string>();
                        while (await dataReader.ReadAsync())
                        {
                            var rowValues = new List<string>();
                            for (int i = 0; i < dataReader.FieldCount; i++)
                            {
                                var value = dataReader.GetValue(i);
                                if (value == DBNull.Value)
                                {
                                    rowValues.Add("NULL");
                                }
                                else if (value is string || value is DateTime)
                                {
                                    rowValues.Add($"'{value.ToString()?.Replace("'", "''")}'");
                                }
                                else
                                {
                                    rowValues.Add(value.ToString() ?? "NULL");
                                }
                            }
                            values.Add($"({string.Join(", ", rowValues)})");
                        }

                        if (values.Count > 0)
                        {
                            sqlBuilder.AppendLine(string.Join(",\n", values) + ";");
                        }
                        sqlBuilder.AppendLine();
                    }
                    dataReader.Close();
                }

                await File.WriteAllTextAsync(filePath, sqlBuilder.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error backing up with direct SQL: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteDatabaseAsync()
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                var connectionStringWithoutDb = $"Server={settings.MySQLServer};User={settings.MySQLUser};Password={settings.MySQLPassword};";

                using var connection = new MySqlConnection(connectionStringWithoutDb);
                await connection.OpenAsync();

                // Drop database
                using var dropCommand = connection.CreateCommand();
                dropCommand.CommandText = $"DROP DATABASE IF EXISTS `{settings.MySQLDatabase}`;";
                await dropCommand.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine($"Database {settings.MySQLDatabase} deleted successfully");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting database: {ex.Message}");
                return false;
            }
        }
    }
}
