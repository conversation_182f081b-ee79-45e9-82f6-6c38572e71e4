using System;
using System.Threading.Tasks;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة إدارة قاعدة البيانات MySQL
    /// </summary>
    public interface IDatabaseService
    {
        /// <summary>
        /// إنشاء قاعدة البيانات والجداول
        /// </summary>
        Task<bool> CreateDatabaseAsync();

        /// <summary>
        /// استيراد قاعدة البيانات من ملف SQL
        /// </summary>
        Task<bool> ImportDatabaseAsync(string filePath);

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        Task<bool> BackupDatabaseAsync(string filePath);

        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// </summary>
        Task<bool> RestoreDatabaseAsync(string filePath);

        /// <summary>
        /// التحقق من وجود قاعدة البيانات
        /// </summary>
        Task<bool> DatabaseExistsAsync();

        /// <summary>
        /// التحقق من وجود الجداول المطلوبة
        /// </summary>
        Task<bool> TablesExistAsync();

        /// <summary>
        /// إنشاء الجداول المطلوبة فقط
        /// </summary>
        Task<bool> CreateTablesAsync();

        /// <summary>
        /// تنفيذ ملف SQL
        /// </summary>
        Task<bool> ExecuteSqlFileAsync(string filePath);

        /// <summary>
        /// تنفيذ استعلام SQL
        /// </summary>
        Task<bool> ExecuteSqlCommandAsync(string sqlCommand);

        /// <summary>
        /// الحصول على معلومات قاعدة البيانات
        /// </summary>
        Task<DatabaseInfo> GetDatabaseInfoAsync();

        /// <summary>
        /// حذف قاعدة البيانات نهائياً
        /// </summary>
        Task<bool> DeleteDatabaseAsync();
    }

    /// <summary>
    /// معلومات قاعدة البيانات
    /// </summary>
    public class DatabaseInfo
    {
        public string DatabaseName { get; set; } = "";
        public bool Exists { get; set; }
        public int TableCount { get; set; }
        public long SizeInBytes { get; set; }
        public string SizeDisplay { get; set; } = "";
        public DateTime LastBackup { get; set; }
        public string Version { get; set; } = "";
        public string Charset { get; set; } = "";
        public string Collation { get; set; } = "";
    }
}
