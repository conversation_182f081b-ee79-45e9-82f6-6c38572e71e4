<Window x:Class="NetworkManagement.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Shabaka Pro - نظام إدارة الشبكات المتقدم"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Icon="pack://application:,,,/Resources/shabaka-pro.ico"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Top Bar -->
        <materialDesign:Card Grid.Row="0"
                           materialDesign:ShadowAssist.ShadowDepth="Depth2"
                           Margin="0,0,0,5">
            <Grid Height="60">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Title and User Info -->
                <StackPanel Grid.Column="0" Orientation="Horizontal"
                           VerticalAlignment="Center" Margin="20,0">
                    <materialDesign:PackIcon Kind="Network"
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="Shabaka Pro"
                              FontSize="18" FontWeight="Bold"
                              VerticalAlignment="Center" Margin="10,0"/>
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"
                              Margin="20,0"/>
                    <TextBlock Text="{Binding CurrentUserName}"
                              FontSize="14" FontWeight="Medium"
                              VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding CurrentUserRole, StringFormat='({0})'}"
                              FontSize="12"
                              VerticalAlignment="Center" Margin="5,0,0,0"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <!-- Notifications Button -->
                <Button x:Name="NotificationsButton"
                       Grid.Column="1"
                       Command="{Binding ToggleNotificationsCommand}"
                       Style="{StaticResource MaterialDesignIconButton}"
                       ToolTip="الإشعارات"
                       Margin="0,0,10,0"
                       VerticalAlignment="Center">
                    <Grid>
                        <materialDesign:PackIcon Kind="Bell" Width="24" Height="24"/>
                        <Border Background="{DynamicResource PrimaryHueMidBrush}"
                               CornerRadius="10"
                               Width="20" Height="20"
                               HorizontalAlignment="Right"
                               VerticalAlignment="Top"
                               Margin="0,-5,-5,0"
                               Visibility="{Binding HasUnreadNotifications, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="{Binding UnreadNotificationsCount}"
                                      Foreground="White"
                                      FontSize="10"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Button>

                <!-- Logout Button -->
                <Button Grid.Column="2"
                       Command="{Binding LogoutCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,20,0"
                       VerticalAlignment="Center">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Logout"
                                                   Width="16" Height="16"
                                                   Margin="0,0,5,0"/>
                            <TextBlock Text="تسجيل الخروج"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <materialDesign:Card Grid.Column="0"
                               materialDesign:ShadowAssist.ShadowDepth="Depth2"
                               Margin="5,0,5,5">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">

                        <!-- Dashboard -->
                        <Button Command="{Binding ShowDashboardCommand}"
                               Style="{StaticResource MenuButtonStyle}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ViewDashboard"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="لوحة التحكم"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <!-- Devices -->
                        <Button Command="{Binding ShowDevicesCommand}"
                               Style="{StaticResource MenuButtonStyle}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Router"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="الأجهزة"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <!-- Sites -->
                        <Button Command="{Binding ShowSitesCommand}"
                               Style="{StaticResource MenuButtonStyle}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="MapMarker"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="المواقع"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <!-- Users -->
                        <Button Command="{Binding ShowUsersCommand}"
                               Style="{StaticResource MenuButtonStyle}"
                               Visibility="{Binding CanManageUsers, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountGroup"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="المستخدمين"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <!-- Tasks -->
                        <Button Command="{Binding ShowTasksCommand}"
                               Style="{StaticResource MenuButtonStyle}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ClipboardList"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="أعمالي"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <!-- Purchases -->
                        <Button Command="{Binding ShowPurchasesCommand}"
                               Style="{StaticResource MenuButtonStyle}"
                               Visibility="{Binding CanManagePurchases, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ShoppingCart"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="المشتريات"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <!-- Inventory -->
                        <Button Command="{Binding ShowInventoryCommand}"
                               Style="{StaticResource MenuButtonStyle}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Package"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="المخزون"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <!-- Reports -->
                        <Button Command="{Binding ShowReportsCommand}"
                               Style="{StaticResource MenuButtonStyle}"
                               Visibility="{Binding CanViewReports, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="التقارير"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>



                        <!-- Network Management -->
                        <Button Command="{Binding ShowNetworkManagementCommand}"
                               Style="{StaticResource MenuButtonStyle}"
                               Visibility="{Binding CanManageNetworks, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Network"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="إدارة الشبكات"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <!-- Settings -->
                        <Button Command="{Binding ShowSettingsCommand}"
                               Style="{StaticResource MenuButtonStyle}"
                               Visibility="{Binding CanViewSettings, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Settings"
                                                           Width="20" Height="20"
                                                           Margin="0,0,10,0"/>
                                    <TextBlock Text="الإعدادات"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- Content Area -->
            <materialDesign:Card Grid.Column="1"
                               materialDesign:ShadowAssist.ShadowDepth="Depth2"
                               Margin="0,0,5,5">
                <ContentPresenter Content="{Binding CurrentView}" Margin="20"/>
            </materialDesign:Card>
        </Grid>

        <!-- Notifications Popup -->
        <Popup x:Name="NotificationsPopup"
               IsOpen="{Binding IsNotificationsPanelOpen}"
               PlacementTarget="{Binding ElementName=NotificationsButton}"
               Placement="Bottom"
               HorizontalOffset="-300"
               VerticalOffset="10"
               StaysOpen="False"
               AllowsTransparency="True">
            <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth3"
                               Width="350"
                               MaxHeight="500">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <Border Grid.Row="0"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Padding="15,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0"
                                      Text="الإشعارات"
                                      Foreground="White"
                                      FontWeight="Medium"
                                      FontSize="16"
                                      VerticalAlignment="Center"/>

                            <Button Grid.Column="1"
                                   Command="{Binding ClearAllNotificationsCommand}"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   ToolTip="مسح الكل"
                                   Foreground="White">
                                <materialDesign:PackIcon Kind="DeleteSweep" Width="20" Height="20"/>
                            </Button>
                        </Grid>
                    </Border>

                    <!-- Notifications List -->
                    <ScrollViewer Grid.Row="1"
                                 VerticalScrollBarVisibility="Auto"
                                 MaxHeight="350">
                        <ItemsControl ItemsSource="{Binding Notifications}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                                           BorderThickness="0,0,0,1"
                                           Padding="15,10">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Icon -->
                                            <materialDesign:PackIcon Grid.Column="0"
                                                                   Kind="{Binding TypeIcon}"
                                                                   Width="24" Height="24"
                                                                   Foreground="{Binding TypeColor}"
                                                                   VerticalAlignment="Top"
                                                                   Margin="0,0,10,0"/>

                                            <!-- Content -->
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Title}"
                                                          FontWeight="Medium"
                                                          TextWrapping="Wrap"
                                                          Margin="0,0,0,5"/>
                                                <TextBlock Text="{Binding Message}"
                                                          TextWrapping="Wrap"
                                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                          FontSize="12"/>
                                            </StackPanel>

                                            <!-- Time -->
                                            <TextBlock Grid.Column="2"
                                                      Text="{Binding TimeAgo}"
                                                      FontSize="10"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      VerticalAlignment="Top"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>

                    <!-- Empty State -->
                    <StackPanel Grid.Row="1"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Visibility="{Binding HasNotifications, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="BellOff"
                                               Width="48" Height="48"
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,10"/>
                        <TextBlock Text="لا توجد إشعارات"
                                  HorizontalAlignment="Center"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>

                    <!-- Footer -->
                    <Border Grid.Row="2"
                           Background="{DynamicResource MaterialDesignCardBackground}"
                           Padding="15,10"
                           BorderBrush="{DynamicResource MaterialDesignDivider}"
                           BorderThickness="0,1,0,0">
                        <Button Content="عرض جميع الإشعارات"
                               Command="{Binding ViewAllNotificationsCommand}"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Center"/>
                    </Border>
                </Grid>
            </materialDesign:Card>
        </Popup>
    </Grid>
</Window>
