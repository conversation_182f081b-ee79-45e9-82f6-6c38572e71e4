# إنشاء قالب Excel لاستيراد الأجهزة
# Create Excel Template for Device Import

param(
    [string]$OutputPath = "قالب_استيراد_الأجهزة_منظم.xlsx"
)

# التحقق من وجود Excel
try {
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    Write-Host "🚀 إنشاء قالب Excel لاستيراد الأجهزة..." -ForegroundColor Green
    Write-Host "🚀 Creating Excel template for device import..." -ForegroundColor Green
    
    # إنشاء مصنف جديد
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.Worksheets.Item(1)
    $worksheet.Name = "قالب الأجهزة"
    
    # تعريف العناوين
    $headers = @(
        "الموقع",
        "النوع", 
        "عنوان IP",
        "المسؤول",
        "الشبكة",
        "طريقة الربط",
        "الشبكة المرتبطة",
        "القناة",
        "الأجهزة المتصلة",
        "طول سلك الشبكة",
        "طول سلك الكهرباء",
        "الحالة",
        "تاريخ التثبيت",
        "آخر فحص"
    )
    
    # إضافة العناوين
    for ($i = 0; $i -lt $headers.Length; $i++) {
        $cell = $worksheet.Cells.Item(1, $i + 1)
        $cell.Value2 = $headers[$i]
        $cell.Font.Bold = $true
        $cell.Font.Size = 12
        $cell.Interior.Color = 15773696  # لون أزرق فاتح
        $cell.Font.Color = 16777215     # لون أبيض
        $cell.HorizontalAlignment = -4108  # توسيط
    }
    
    # إضافة بيانات تجريبية
    $sampleData = @(
        @("المكتب الرئيسي", "راوتر", "***********", "أحمد محمد", "الشبكة الرئيسية", "كابل", "شبكة فرعية 1", "6", "25", "50", "30", "نشط", "2024-01-15", "2024-12-01"),
        @("الفرع الأول", "سويتش", "***********0", "سارة أحمد", "الشبكة الفرعية", "كابل", "الشبكة الرئيسية", "11", "15", "25", "20", "نشط", "2024-02-10", "2024-11-28"),
        @("المستودع", "نقطة وصول", "************", "محمد علي", "شبكة المستودع", "لاسلكي", "الشبكة الرئيسية", "1", "8", "15", "10", "صيانة", "2024-03-05", "2024-11-25"),
        @("القاعة الكبرى", "كاميرا IP", "************", "فاطمة خالد", "شبكة المراقبة", "كابل", "", "", "", "30", "25", "نشط", "2024-04-12", "2024-12-02"),
        @("المدخل الرئيسي", "جهاز أمان", "************", "عبدالله سعد", "شبكة الأمان", "كابل", "", "", "", "20", "15", "غير نشط", "2024-05-20", "2024-11-30")
    )
    
    # إضافة البيانات التجريبية
    for ($row = 0; $row -lt $sampleData.Length; $row++) {
        for ($col = 0; $col -lt $sampleData[$row].Length; $col++) {
            $cell = $worksheet.Cells.Item($row + 2, $col + 1)
            $cell.Value2 = $sampleData[$row][$col]
            
            # تنسيق خاص للأعمدة المطلوبة (الأربعة الأولى)
            if ($col -lt 4) {
                $cell.Interior.Color = 16777164  # لون أصفر فاتح
            }
        }
    }
    
    # تنسيق الأعمدة
    $range = $worksheet.UsedRange
    $range.Columns.AutoFit()
    $range.Borders.LineStyle = 1
    $range.Borders.Weight = 2
    
    # إضافة ملاحظات
    $notesRow = $sampleData.Length + 4
    $worksheet.Cells.Item($notesRow, 1).Value2 = "ملاحظات مهمة:"
    $worksheet.Cells.Item($notesRow, 1).Font.Bold = $true
    $worksheet.Cells.Item($notesRow, 1).Font.Size = 14
    $worksheet.Cells.Item($notesRow, 1).Font.Color = 255  # أحمر
    
    $notes = @(
        "• الأعمدة الأربعة الأولى (الموقع، النوع، عنوان IP، المسؤول) مطلوبة ولا يمكن تركها فارغة",
        "• باقي الأعمدة اختيارية ويمكن تركها فارغة",
        "• تأكد من عدم تكرار عنوان IP",
        "• استخدم تنسيق التاريخ: YYYY-MM-DD (مثل: 2024-12-01)",
        "• الحالات المتاحة: نشط، غير نشط، صيانة، غير محدد",
        "• احذف هذه الصفوف قبل الاستيراد واتركها فقط للمرجع"
    )
    
    for ($i = 0; $i -lt $notes.Length; $i++) {
        $worksheet.Cells.Item($notesRow + $i + 1, 1).Value2 = $notes[$i]
        $worksheet.Cells.Item($notesRow + $i + 1, 1).Font.Size = 10
    }
    
    # حفظ الملف
    $fullPath = Join-Path (Get-Location) $OutputPath
    $workbook.SaveAs($fullPath, 51)  # 51 = xlOpenXMLWorkbook (.xlsx)
    
    Write-Host "✅ تم إنشاء القالب بنجاح!" -ForegroundColor Green
    Write-Host "✅ Template created successfully!" -ForegroundColor Green
    Write-Host "📁 موقع الملف: $fullPath" -ForegroundColor Cyan
    Write-Host "📁 File location: $fullPath" -ForegroundColor Cyan
    
    # إغلاق Excel
    $workbook.Close()
    $excel.Quit()
    
    # تنظيف الذاكرة
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($worksheet) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
    
    Write-Host ""
    Write-Host "🎯 الخطوات التالية:" -ForegroundColor Yellow
    Write-Host "🎯 Next steps:" -ForegroundColor Yellow
    Write-Host "1. افتح الملف المُنشأ" -ForegroundColor White
    Write-Host "2. انسخ بياناتك من الملف القديم إلى الأعمدة المناسبة" -ForegroundColor White
    Write-Host "3. احذف الصفوف التجريبية والملاحظات" -ForegroundColor White
    Write-Host "4. احفظ الملف واستورده في التطبيق" -ForegroundColor White
    
} catch {
    Write-Host "❌ خطأ في إنشاء الملف: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Error creating file: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 تأكد من:" -ForegroundColor Yellow
    Write-Host "• تثبيت Microsoft Excel على الجهاز" -ForegroundColor White
    Write-Host "• إغلاق أي ملفات Excel مفتوحة" -ForegroundColor White
    Write-Host "• وجود صلاحيات الكتابة في المجلد" -ForegroundColor White
}

Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
