using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Services;
using NetworkManagement.Models;
using NetworkManagement.Views;
using NetworkManagement.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace NetworkManagement.ViewModels
{
    public partial class TasksViewModel : ObservableObject, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;
        private readonly IReportExportService _exportService;

        [ObservableProperty]
        private ObservableCollection<Models.Task> tasks = new();

        [ObservableProperty]
        private ObservableCollection<Models.Task> filteredTasks = new();

        [ObservableProperty]
        private Models.Task? selectedTask;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string statusFilter = "الكل";

        [ObservableProperty]
        private string priorityFilter = "الكل";

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private int pendingTasksCount = 0;

        [ObservableProperty]
        private int inProgressTasksCount = 0;

        [ObservableProperty]
        private int completedTasksCount = 0;

        // خصائص الصلاحيات
        public bool CanAddTasks => _authService.CanAddData();
        public bool CanEditTasks => _authService.CanEditData();
        public bool CanDeleteTasks => _authService.CanDeleteData();
        public bool CanManageTasks => _authService.CanManageTasks;

        // Threading control
        private readonly System.Threading.SemaphoreSlim _loadSemaphore = new(1, 1);
        private System.Threading.CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new();

        public TasksViewModel(IServiceProvider serviceProvider, IAuthService authService, IReportExportService exportService)
        {
            _serviceProvider = serviceProvider;
            _authService = authService;
            _exportService = exportService;

            // الاشتراك في تغيير المستخدم لتحديث الصلاحيات
            _authService.UserChanged += OnUserChanged;
        }

        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                await LoadTasksAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing tasks: {ex.Message}");
                MessageBox.Show(
                    $"حدث خطأ أثناء تحميل المهام:\n{ex.Message}",
                    "خطأ في التحميل",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadTasksAsync()
        {
            // منع التداخل في العمليات
            if (!await _loadSemaphore.WaitAsync(100))
            {
                System.Diagnostics.Debug.WriteLine("LoadTasksAsync: عملية أخرى قيد التنفيذ، تم التجاهل");
                return;
            }

            try
            {
                // إلغاء أي عملية سابقة
                lock (_loadLock)
                {
                    _loadCancellationTokenSource?.Cancel();
                    _loadCancellationTokenSource = new System.Threading.CancellationTokenSource();
                }

                IsLoading = true;
                var cancellationToken = _loadCancellationTokenSource.Token;

                // إنشاء scope جديد للحصول على TaskService
                using var scope = _serviceProvider.CreateScope();
                var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

                var allTasks = await taskService.GetAllAsync();

                // التحقق من الإلغاء
                cancellationToken.ThrowIfCancellationRequested();

                // فلترة المهام حسب الصلاحيات
                var filteredTasks = allTasks.Where(task => _authService.CanViewData(task.NetworkId)).ToList();

                // التحقق من الإلغاء مرة أخرى
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث UI على الـ UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Tasks.Clear();
                    foreach (var task in filteredTasks)
                    {
                        Tasks.Add(task);
                    }
                    ApplyFilters();
                    UpdateTaskCounts();
                }, System.Windows.Threading.DispatcherPriority.Normal, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("LoadTasksAsync: تم إلغاء العملية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading tasks: {ex.Message}");

                // عرض رسالة الخطأ على UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    MessageBox.Show(
                        $"خطأ في تحميل المهام: {ex.Message}",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                });
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();
            }
        }

        private void ApplyFilters()
        {
            var filtered = Tasks.AsEnumerable();

            // فلتر حسب الحالة
            if (StatusFilter != "الكل")
            {
                var statusKey = GetStatusKey(StatusFilter);
                filtered = filtered.Where(t => t.Status == statusKey);
            }

            // فلتر حسب الأولوية
            if (PriorityFilter != "الكل")
            {
                var priorityKey = GetPriorityKey(PriorityFilter);
                filtered = filtered.Where(t => t.Priority == priorityKey);
            }

            // فلتر حسب النص
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filtered = filtered.Where(t =>
                    t.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    (t.Notes?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            FilteredTasks.Clear();
            foreach (var task in filtered.OrderByDescending(t => t.CreatedAt))
            {
                FilteredTasks.Add(task);
            }
        }

        private void UpdateTaskCounts()
        {
            PendingTasksCount = Tasks.Count(t => t.Status == "pending");
            InProgressTasksCount = Tasks.Count(t => t.Status == "in-progress");
            CompletedTasksCount = Tasks.Count(t => t.Status == "completed");
        }

        private static string GetStatusKey(string statusDisplay)
        {
            return statusDisplay switch
            {
                "معلق" => "pending",
                "قيد التنفيذ" => "in-progress",
                "مكتمل" => "completed",
                "ملغي" => "cancelled",
                _ => "pending"
            };
        }

        private static string GetPriorityKey(string priorityDisplay)
        {
            return priorityDisplay switch
            {
                "منخفضة" => "low",
                "متوسطة" => "medium",
                "عالية" => "high",
                "عاجلة" => "urgent",
                _ => "medium"
            };
        }

        // Property Changed Handlers
        partial void OnStatusFilterChanged(string value)
        {
            ApplyFilters();
        }

        partial void OnPriorityFilterChanged(string value)
        {
            ApplyFilters();
        }

        partial void OnSearchTextChanged(string value)
        {
            ApplyFilters();
        }

        // Commands
        [RelayCommand]
        private async System.Threading.Tasks.Task RefreshAsync()
        {
            await LoadTasksAsync();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task AddTaskAsync()
        {
            // التحقق من صلاحية إضافة المهام
            if (!CanAddTasks)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "مهام جديدة");
                return;
            }

            try
            {
                var dialog = new TaskDialogView();

                if (dialog.ShowDialog() == true)
                {
                    await LoadTasksAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فتح نافذة إضافة المهمة:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task EditTaskAsync(Models.Task? task = null)
        {
            var taskToEdit = task ?? SelectedTask;
            if (taskToEdit == null) return;

            // التحقق من صلاحية تعديل المهمة
            if (!PermissionHelper.CanEditItem(_authService, taskToEdit.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذه المهمة");
                return;
            }

            try
            {
                var dialog = new TaskDialogView(taskToEdit);

                if (dialog.ShowDialog() == true)
                {
                    await LoadTasksAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فتح نافذة تحرير المهمة:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task CompleteTaskAsync(Models.Task? task = null)
        {
            var taskToComplete = task ?? SelectedTask;
            if (taskToComplete != null && taskToComplete.Status != "completed")
            {
                // التحقق من صلاحية تعديل المهمة
                if (!_authService.CanEditData(taskToComplete.NetworkId))
                {
                    MessageBox.Show(
                        "ليس لديك صلاحية لتعديل هذه المهمة",
                        "غير مسموح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return;
                }

                try
                {
                    taskToComplete.Status = "completed";
                    taskToComplete.CompletedAt = DateTime.Now;
                    taskToComplete.UpdatedAt = DateTime.Now;

                    using var scope = _serviceProvider.CreateScope();
                    var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

                    await taskService.UpdateAsync(taskToComplete);
                    await LoadTasksAsync();

                    MessageBox.Show(
                        "تم إكمال المهمة بنجاح",
                        "تم الإكمال",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"خطأ في إكمال المهمة:\n{ex.Message}",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteTaskAsync(Models.Task? task = null)
        {
            var taskToDelete = task ?? SelectedTask;
            if (taskToDelete == null) return;

            // التحقق من صلاحية حذف المهمة
            if (!PermissionHelper.CanDeleteItem(_authService, taskToDelete.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذه المهمة");
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المهمة:\n{taskToDelete.Description}؟\n\nلا يمكن التراجع عن هذا الإجراء.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

                    await taskService.DeleteAsync(taskToDelete.Id);
                    await LoadTasksAsync();

                    MessageBox.Show(
                        "تم حذف المهمة بنجاح",
                        "تم الحذف",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"خطأ في حذف المهمة:\n{ex.Message}",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ExportTasksAsync()
        {
            try
            {
                IsLoading = true;

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var defaultFileName = $"المهام_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                // استخدام المهام المفلترة الحالية
                var tasksToExport = FilteredTasks.ToList();
                result = isExcel
                    ? await _exportService.ExportTasksReportToExcelAsync(tasksToExport, filePath)
                    : await _exportService.ExportTasksReportToCsvAsync(tasksToExport, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    MessageBox.Show(
                        $"تم تصدير {tasksToExport.Count} مهمة بنجاح إلى:\n{result}",
                        "تم التصدير",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء تصدير المهام:\n{ex.Message}",
                    "خطأ في التصدير",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddTasks));
                    OnPropertyChanged(nameof(CanEditTasks));
                    OnPropertyChanged(nameof(CanDeleteTasks));
                    OnPropertyChanged(nameof(CanManageTasks));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadTasksAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // إلغاء الاشتراك في تغيير المستخدم
            _authService.UserChanged -= OnUserChanged;

            // إلغاء أي عمليات تحميل جارية
            lock (_loadLock)
            {
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource?.Dispose();
            }

            // تنظيف SemaphoreSlim
            _loadSemaphore?.Dispose();

            // تنظيف الموارد
            Tasks.Clear();
            FilteredTasks.Clear();
            GC.SuppressFinalize(this);
        }
    }
}
