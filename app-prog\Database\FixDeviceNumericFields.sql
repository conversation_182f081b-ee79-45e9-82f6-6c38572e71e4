-- إصلاح الحقول الرقمية في جدول الأجهزة
-- Fix numeric fields in Devices table

-- تحديث الحقول الرقمية التي تحتوي على قيم غير صالحة
-- Update numeric fields that contain invalid values

-- إصلاح Channel - تحويل القيم غير الرقمية إلى NULL
UPDATE Devices 
SET Channel = NULL 
WHERE Channel IS NOT NULL 
AND (
    CAST(Channel AS CHAR) NOT REGEXP '^[0-9]+$'
    OR Channel = ''
    OR Channel = '0'
);

-- إصلاح ConnectedDevices - تحويل القيم غير الرقمية إلى NULL
UPDATE Devices 
SET ConnectedDevices = NULL 
WHERE ConnectedDevices IS NOT NULL 
AND (
    CAST(ConnectedDevices AS CHAR) NOT REGEXP '^[0-9]+$'
    OR ConnectedDevices = ''
);

-- إصلاح NetworkCableLength - تحويل القيم غير الرقمية إلى NULL
UPDATE Devices 
SET NetworkCableLength = NULL 
WHERE NetworkCableLength IS NOT NULL 
AND (
    CAST(NetworkCableLength AS CHAR) NOT REGEXP '^[0-9]+$'
    OR NetworkCableLength = ''
);

-- إصلاح PowerCableLength - تحويل القيم غير الرقمية إلى NULL
UPDATE Devices 
SET PowerCableLength = NULL 
WHERE PowerCableLength IS NOT NULL 
AND (
    CAST(PowerCableLength AS CHAR) NOT REGEXP '^[0-9]+$'
    OR PowerCableLength = ''
);

-- التحقق من النتائج
SELECT 
    'تم إصلاح الحقول الرقمية في جدول الأجهزة' as Result,
    COUNT(*) as TotalDevices
FROM Devices;

-- عرض الأجهزة التي تحتوي على قيم رقمية صالحة
SELECT 
    Id,
    Responsible,
    Channel,
    ConnectedDevices,
    NetworkCableLength,
    PowerCableLength
FROM Devices 
WHERE Channel IS NOT NULL 
   OR ConnectedDevices IS NOT NULL 
   OR NetworkCableLength IS NOT NULL 
   OR PowerCableLength IS NOT NULL
LIMIT 10;
