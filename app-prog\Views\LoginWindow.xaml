<Window x:Class="NetworkManagement.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - Shabaka Pro"
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Icon="pack://application:,,,/Resources/shabaka-pro.ico"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        KeyDown="OnKeyDown">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <Border Grid.Row="0" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                CornerRadius="8"
                Margin="20">
            <StackPanel Margin="40" VerticalAlignment="Center">
                
                <!-- Logo and Title -->
                <materialDesign:PackIcon Kind="Monitor" 
                                       Width="64" Height="64"
                                       HorizontalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                       Margin="0,0,0,20"/>
                
                <TextBlock Text="Shabaka Pro"
                          FontSize="24"
                          FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"/>
                
                <TextBlock Text="تسجيل الدخول"
                          FontSize="16"
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          Margin="0,0,0,30"/>

                <!-- Username Field -->
                <TextBox x:Name="UsernameTextBox"
                        materialDesign:HintAssist.Hint="اسم المستخدم"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,20"
                        FontSize="14"
                        KeyDown="OnKeyDown"/>

                <!-- Password Field -->
                <PasswordBox x:Name="PasswordBox"
                            materialDesign:HintAssist.Hint="كلمة المرور"
                            materialDesign:HintAssist.IsFloating="True"
                            PasswordChanged="PasswordBox_PasswordChanged"
                            Margin="0,0,0,20"
                            FontSize="14"
                            KeyDown="OnKeyDown"/>

                <!-- Remember Me -->
                <CheckBox Content="تذكرني"
                         IsChecked="{Binding RememberMe}"
                         Margin="0,0,0,20"
                         HorizontalAlignment="Right"/>

                <!-- Error Message -->
                <Border Background="#FFEBEE"
                       BorderBrush="#F44336"
                       BorderThickness="1"
                       CornerRadius="4"
                       Padding="12"
                       Margin="0,0,0,20"
                       Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AlertCircle"
                                               Foreground="#F44336"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="{Binding ErrorMessage}"
                                  Foreground="#F44336"
                                  TextWrapping="Wrap"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Login Button -->
                <Button Content="تسجيل الدخول"
                       Command="{Binding LoginCommand}"
                       IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="{DynamicResource PrimaryHueMidBrush}"
                       Foreground="White"
                       Height="40"
                       FontSize="14"
                       Margin="0,0,0,15"/>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,0,0,15"/>

                <!-- Forgot Password -->
                <Button Content="نسيت كلمة المرور؟"
                       Command="{Binding ForgotPasswordCommand}"
                       Style="{StaticResource MaterialDesignFlatButton}"
                       HorizontalAlignment="Center"
                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>

            </StackPanel>
        </Border>

        <!-- Footer -->
        <TextBlock Grid.Row="1"
                  Text="© 2024 Shabaka Pro. جميع الحقوق محفوظة."
                  HorizontalAlignment="Center"
                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                  FontSize="12"
                  Margin="0,0,0,10"/>
    </Grid>
</Window>
