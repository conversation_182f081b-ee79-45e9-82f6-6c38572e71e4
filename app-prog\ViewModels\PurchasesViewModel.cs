using System;
using System.Collections.ObjectModel;
using NetworkManagement.Views;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace NetworkManagement.ViewModels
{
    public partial class PurchasesViewModel : ObservableObject
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;

        [ObservableProperty]
        private ObservableCollection<Purchase> purchases = new();

        [ObservableProperty]
        private Purchase? selectedPurchase;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private string selectedCategory = "الكل";

        [ObservableProperty]
        private DateTime? startDate;

        [ObservableProperty]
        private DateTime? endDate;

        [ObservableProperty]
        private bool isLoading = false;

        // Statistics
        [ObservableProperty]
        private decimal totalSpending;

        [ObservableProperty]
        private decimal monthlySpending;

        [ObservableProperty]
        private decimal yearlySpending;

        [ObservableProperty]
        private int totalPurchases;

        // خصائص الصلاحيات
        public bool CanAddPurchases => _authService.CanAddData();
        public bool CanEditPurchases => _authService.CanEditData();
        public bool CanDeletePurchases => _authService.CanDeleteData();
        public bool CanManagePurchases => _authService.CanManagePurchases;

        // Threading control
        private readonly System.Threading.SemaphoreSlim _loadSemaphore = new(1, 1);
        private System.Threading.CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new();

        public string[] Categories { get; } = { "الكل", "أجهزة", "كابلات", "أدوات", "صيانة", "أخرى" };

        public PurchasesViewModel(IServiceProvider serviceProvider, IAuthService authService)
        {
            _serviceProvider = serviceProvider;
            _authService = authService;

            // الاشتراك في تغيير المستخدم لتحديث الصلاحيات
            _authService.UserChanged += OnUserChanged;

            // Set default date range (current month)
            var now = DateTime.Now;
            StartDate = new DateTime(now.Year, now.Month, 1);
            EndDate = StartDate.Value.AddMonths(1).AddDays(-1);
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadPurchasesAsync()
        {
            // منع التداخل في العمليات
            if (!await _loadSemaphore.WaitAsync(100))
            {
                System.Diagnostics.Debug.WriteLine("LoadPurchasesAsync: عملية أخرى قيد التنفيذ، تم التجاهل");
                return;
            }

            try
            {
                // إلغاء أي عملية سابقة
                lock (_loadLock)
                {
                    _loadCancellationTokenSource?.Cancel();
                    _loadCancellationTokenSource = new System.Threading.CancellationTokenSource();
                }

                IsLoading = true;
                var cancellationToken = _loadCancellationTokenSource.Token;

                // إنشاء scope جديد للحصول على PurchaseService
                using var scope = _serviceProvider.CreateScope();
                var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();

                var purchaseList = await purchaseService.GetAllAsync();

                // التحقق من الإلغاء
                cancellationToken.ThrowIfCancellationRequested();

                // فلترة المشتريات حسب الصلاحيات أولاً
                purchaseList = purchaseList.Where(purchase => _authService.CanViewData(purchase.NetworkId));

                // Apply filters
                if (!string.IsNullOrEmpty(SearchText))
                {
                    purchaseList = purchaseList.Where(p =>
                        p.ItemType.Contains(SearchText) ||
                        (p.Description?.Contains(SearchText) ?? false) ||
                        (p.Supplier?.Contains(SearchText) ?? false));
                }

                if (SelectedCategory != "الكل")
                {
                    purchaseList = purchaseList.Where(p => p.Category == SelectedCategory);
                }

                if (StartDate.HasValue)
                {
                    purchaseList = purchaseList.Where(p => p.Date >= StartDate.Value);
                }

                if (EndDate.HasValue)
                {
                    purchaseList = purchaseList.Where(p => p.Date <= EndDate.Value);
                }

                // التحقق من الإلغاء مرة أخرى
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث UI على الـ UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Purchases.Clear();
                    foreach (var purchase in purchaseList.OrderByDescending(p => p.Date))
                    {
                        Purchases.Add(purchase);
                    }
                }, System.Windows.Threading.DispatcherPriority.Normal, cancellationToken);

                await LoadStatisticsAsync();
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("LoadPurchasesAsync: تم إلغاء العملية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading purchases: {ex.Message}");

                // عرض رسالة الخطأ على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    System.Windows.MessageBox.Show(
                        $"خطأ في تحميل المشتريات: {ex.Message}",
                        "خطأ",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                });
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SearchPurchasesAsync()
        {
            await LoadPurchasesAsync();
        }

        [RelayCommand]
        private void AddPurchase()
        {
            // التحقق من صلاحية إضافة المشتريات
            if (!CanAddPurchases)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "مشتريات جديدة");
                return;
            }

            var dialogViewModel = App.GetService<PurchaseDialogViewModel>();
            var dialog = new PurchaseDialog(dialogViewModel);

            dialogViewModel.PurchaseSaved += async (s, purchase) =>
            {
                await LoadPurchasesAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private void EditPurchase()
        {
            var purchaseToEdit = SelectedPurchase;
            if (purchaseToEdit == null) return;

            // التحقق من صلاحية تعديل المشترى
            if (!PermissionHelper.CanEditItem(_authService, purchaseToEdit.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا المشترى");
                return;
            }

            var dialogViewModel = App.GetService<PurchaseDialogViewModel>();
            dialogViewModel.SetEditPurchase(purchaseToEdit);
            var dialog = new PurchaseDialog(dialogViewModel);

            dialogViewModel.PurchaseSaved += async (s, purchase) =>
            {
                await LoadPurchasesAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeletePurchaseAsync()
        {
            var purchaseToDelete = SelectedPurchase;
            if (purchaseToDelete == null) return;

            // التحقق من صلاحية حذف المشترى
            if (!PermissionHelper.CanDeleteItem(_authService, purchaseToDelete.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا المشترى");
                return;
            }

            var purchaseInfo = $"{purchaseToDelete.ItemType} - {purchaseToDelete.Date:yyyy/MM/dd}";
            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف المشترى:\n{purchaseInfo}؟\n\nلا يمكن التراجع عن هذا الإجراء.",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();

                    await purchaseService.DeleteAsync(purchaseToDelete.Id);
                    await LoadPurchasesAsync();

                    System.Windows.MessageBox.Show(
                        $"تم حذف المشترى '{purchaseInfo}' بنجاح.",
                        "تم الحذف",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show(
                        $"حدث خطأ أثناء حذف المشترى:\n{ex.Message}",
                        "خطأ في الحذف",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private async System.Threading.Tasks.Task LoadStatisticsAsync()
        {
            try
            {
                // Total spending (filtered) - السعر كما هو مكتوب (إجمالي للكمية)
                TotalSpending = Purchases.Sum(p => p.Price);
                TotalPurchases = Purchases.Count;

                // إنشاء scope جديد للحصول على PurchaseService
                using var scope = _serviceProvider.CreateScope();
                var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();

                // Monthly spending
                var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                MonthlySpending = await purchaseService.GetTotalSpentAsync(null, monthStart, monthEnd);

                // Yearly spending
                var yearStart = new DateTime(DateTime.Now.Year, 1, 1);
                var yearEnd = new DateTime(DateTime.Now.Year, 12, 31);
                YearlySpending = await purchaseService.GetTotalSpentAsync(null, yearStart, yearEnd);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
            }
        }

        partial void OnSelectedCategoryChanged(string value)
        {
            _ = LoadPurchasesAsync();
        }

        partial void OnStartDateChanged(DateTime? value)
        {
            _ = LoadPurchasesAsync();
        }

        partial void OnEndDateChanged(DateTime? value)
        {
            _ = LoadPurchasesAsync();
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddPurchases));
                    OnPropertyChanged(nameof(CanEditPurchases));
                    OnPropertyChanged(nameof(CanDeletePurchases));
                    OnPropertyChanged(nameof(CanManagePurchases));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadPurchasesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // إلغاء الاشتراك في تغيير المستخدم
            _authService.UserChanged -= OnUserChanged;

            // إلغاء أي عمليات تحميل جارية
            lock (_loadLock)
            {
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource?.Dispose();
            }

            // تنظيف SemaphoreSlim
            _loadSemaphore?.Dispose();

            // تنظيف الموارد
            Purchases.Clear();
        }
    }
}
