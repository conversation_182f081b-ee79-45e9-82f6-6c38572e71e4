<UserControl x:Class="NetworkManagement.Views.PurchasesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel>
            <!-- Page Header -->
            <TextBlock Text="إدارة المشتريات" Style="{StaticResource PageHeaderStyle}"/>

            <!-- Statistics Cards -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Spending Card -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="24" Height="24" 
                                                   Foreground="#4CAF50"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إجمالي الإنفاق" 
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalSpending, StringFormat='{}{0:C}'}" 
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="#4CAF50"/>
                        <TextBlock Text="{Binding TotalPurchases, StringFormat='من {0} عملية شراء'}" 
                                  FontSize="12" 
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Monthly Spending Card -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="Calendar" 
                                                   Width="24" Height="24" 
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إنفاق الشهر" 
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding MonthlySpending, StringFormat='{}{0:C}'}" 
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Yearly Spending Card -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="ChartLine" 
                                                   Width="24" Height="24" 
                                                   Foreground="#FF9800"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إنفاق السنة" 
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding YearlySpending, StringFormat='{}{0:C}'}" 
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="#FF9800"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Quick Actions Card -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="إجراءات سريعة" 
                                  FontSize="14" FontWeight="Medium"
                                  Margin="0,0,0,15"/>
                        
                        <Button Content="إضافة مشترى"
                               Command="{Binding AddPurchaseCommand}"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Margin="0,0,0,5" HorizontalAlignment="Stretch"
                               Visibility="{Binding CanAddPurchases, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        
                        <Button Content="تصدير التقرير" 
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               HorizontalAlignment="Stretch"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Filters -->
            <materialDesign:Card Margin="0,0,0,10" Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- First Row: Search and Category -->
                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Search -->
                        <TextBox Grid.Column="0" 
                                materialDesign:HintAssist.Hint="البحث في المشتريات..."
                                materialDesign:HintAssist.IsFloating="False"
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                Margin="0,0,15,0">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding SearchPurchasesCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>

                        <!-- Category Filter -->
                        <ComboBox Grid.Column="1" 
                                 materialDesign:HintAssist.Hint="فلترة حسب الفئة"
                                 ItemsSource="{Binding Categories}"
                                 SelectedItem="{Binding SelectedCategory}"
                                 MinWidth="150" Margin="0,0,15,0"/>

                        <!-- Action Buttons -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <Button Command="{Binding SearchPurchasesCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Margin="0,0,10,0">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="بحث"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Command="{Binding LoadPurchasesCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="تحديث"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>
                        </StackPanel>
                    </Grid>

                    <!-- Second Row: Date Range -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="من تاريخ:" 
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>
                        
                        <DatePicker Grid.Column="1" 
                                   SelectedDate="{Binding StartDate}"
                                   Margin="0,0,20,0"/>

                        <TextBlock Grid.Column="2" Text="إلى تاريخ:" 
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>
                        
                        <DatePicker Grid.Column="3" 
                                   SelectedDate="{Binding EndDate}"/>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Purchases List -->
            <materialDesign:Card>
                <Grid>
                    <!-- Loading Indicator -->
                    <ProgressBar IsIndeterminate="True" 
                                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                VerticalAlignment="Top" Height="4"/>

                    <!-- Data Grid -->
                    <DataGrid ItemsSource="{Binding Purchases}"
                             SelectedItem="{Binding SelectedPurchase}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             Margin="10">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="نوع العنصر" Binding="{Binding ItemType}" Width="150"/>
                            <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat=C}" Width="100"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                            <DataGridTextColumn Header="المورد" Binding="{Binding Supplier}" Width="120"/>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="100"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="100"/>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                            
                            <!-- Actions Column -->
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="تحرير"
                                                   Command="{Binding DataContext.EditPurchaseCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="0,0,5,0"
                                                   Visibility="{Binding Converter={StaticResource CanEditDataConverter}}"/>
                                            <Button Content="حذف"
                                                   Command="{Binding DataContext.DeletePurchaseCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Foreground="Red"
                                                   Visibility="{Binding Converter={StaticResource CanDeleteDataConverter}}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Empty State -->
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center"
                               Visibility="{Binding Purchases.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="ShoppingCart" Width="64" Height="64" 
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="لا توجد مشتريات" 
                                  FontSize="16" 
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="انقر على 'إضافة مشترى' لإضافة المشترى الأول" 
                                  FontSize="12" 
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</UserControl>
