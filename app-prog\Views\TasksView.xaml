<UserControl x:Class="NetworkManagement.Views.TasksView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <TextBlock Grid.Row="0" Text="أعمالي" Style="{StaticResource PageHeaderStyle}"/>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Filters -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <!-- Status Filter -->
                    <ComboBox materialDesign:HintAssist.Hint="فلترة حسب الحالة"
                             SelectedValue="{Binding StatusFilter}"
                             MinWidth="150" Margin="0,0,15,0">
                        <ComboBoxItem Content="الكل"/>
                        <ComboBoxItem Content="معلق"/>
                        <ComboBoxItem Content="قيد التنفيذ"/>
                        <ComboBoxItem Content="مكتمل"/>
                        <ComboBoxItem Content="ملغي"/>
                    </ComboBox>

                    <!-- Priority Filter -->
                    <ComboBox materialDesign:HintAssist.Hint="فلترة حسب الأولوية"
                             SelectedValue="{Binding PriorityFilter}"
                             MinWidth="150" Margin="0,0,15,0">
                        <ComboBoxItem Content="الكل"/>
                        <ComboBoxItem Content="منخفضة"/>
                        <ComboBoxItem Content="متوسطة"/>
                        <ComboBoxItem Content="عالية"/>
                        <ComboBoxItem Content="عاجلة"/>
                    </ComboBox>

                    <!-- Search Box -->
                    <TextBox materialDesign:HintAssist.Hint="البحث في المهام..."
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            MinWidth="200">
                        <TextBox.Style>
                            <Style TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="">
                                        <Setter Property="materialDesign:HintAssist.Hint" Value="البحث في الوصف والملاحظات..."/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>
                </StackPanel>

                <!-- Spacer -->
                <Grid Grid.Column="1"/>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Command="{Binding ExportTasksCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Export" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تصدير"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding RefreshCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تحديث"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding AddTaskCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Visibility="{Binding CanAddTasks, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة مهمة"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Tasks List -->
        <materialDesign:Card Grid.Row="2">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="15">
                    <ItemsControl ItemsSource="{Binding FilteredTasks}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                    <materialDesign:Card Margin="0,0,0,10" Padding="15">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Task Header -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal" Margin="0,0,0,5">
                                <materialDesign:PackIcon Kind="ClipboardText" Width="20" Height="20"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding Description}"
                                          FontSize="16" FontWeight="Medium"
                                          VerticalAlignment="Center" Margin="10,0,0,0"/>
                            </StackPanel>

                            <!-- Status Badge -->
                            <Border Grid.Row="0" Grid.Column="1"
                                   Background="{Binding StatusColor}" CornerRadius="12"
                                   Padding="8,4" VerticalAlignment="Center">
                                <TextBlock Text="{Binding StatusDisplay}" Foreground="White" FontSize="12" FontWeight="Medium"/>
                            </Border>

                            <!-- Task Description -->
                            <TextBlock Grid.Row="1" Grid.ColumnSpan="2"
                                      Text="{Binding Notes}"
                                      FontSize="14"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      TextWrapping="Wrap" Margin="30,5,0,10"/>

                            <!-- Task Footer -->
                            <Grid Grid.Row="2" Grid.ColumnSpan="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Orientation="Horizontal">
                                    <!-- تاريخ الطلب -->
                                    <materialDesign:PackIcon Kind="CalendarPlus" Width="16" Height="16"
                                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding CreatedAtDisplay, StringFormat='تاريخ الطلب: {0}'}"
                                              FontSize="12"
                                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                                              VerticalAlignment="Center" Margin="5,0,15,0"/>

                                    <!-- تاريخ الإكمال (يظهر فقط للمهام المكتملة) -->
                                    <materialDesign:PackIcon Kind="CalendarCheck" Width="16" Height="16"
                                                           Foreground="#4CAF50"
                                                           VerticalAlignment="Center"
                                                           Visibility="{Binding IsCompleted, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    <TextBlock Text="{Binding CompletedAtDisplay, StringFormat='تاريخ الإكمال: {0}'}"
                                              FontSize="12"
                                              Foreground="#4CAF50"
                                              VerticalAlignment="Center" Margin="5,0,15,0"
                                              Visibility="{Binding IsCompleted, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                    <!-- الأولوية -->
                                    <materialDesign:PackIcon Kind="Flag" Width="16" Height="16"
                                                           Foreground="{Binding PriorityColor}"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding PriorityDisplay}"
                                              FontSize="12"
                                              Foreground="{Binding PriorityColor}"
                                              VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button Content="تحرير"
                                           Command="{Binding DataContext.EditTaskCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           Style="{StaticResource MaterialDesignFlatButton}"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           Margin="0,0,5,0"
                                           Visibility="{Binding Converter={StaticResource CanEditDataConverter}}"/>
                                    <Button Content="إكمال"
                                           Command="{Binding DataContext.CompleteTaskCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           Style="{StaticResource MaterialDesignFlatButton}"
                                           Foreground="#4CAF50"
                                           Visibility="{Binding CanComplete, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    <Button Content="حذف"
                                           Command="{Binding DataContext.DeleteTaskCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           Style="{StaticResource MaterialDesignFlatButton}"
                                           Foreground="Red"
                                           Margin="5,0,0,0"
                                           Visibility="{Binding Converter={StaticResource CanDeleteDataConverter}}"/>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </materialDesign:Card>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                    <!-- Empty State (when no tasks) -->
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,50,0,0"
                               Visibility="{Binding FilteredTasks.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="ClipboardList" Width="64" Height="64"
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="لا توجد مهام"
                                  FontSize="16"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="انقر على 'إضافة مهمة' لإضافة المهمة الأولى"
                                  FontSize="12"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>
    </Grid>
</UserControl>
