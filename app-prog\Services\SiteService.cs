using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class SiteService : ISiteService
    {
        private readonly NetworkDbContext _context;

        public SiteService(NetworkDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Site>> GetAllAsync(string? networkFilter = null)
        {
            var query = _context.Sites?.Include(s => s.Devices).Include(s => s.Network).AsQueryable() ?? 
                throw new InvalidOperationException("DbSet<Site> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(s => s.NetworkId == networkFilter);
            }

            return await query.OrderBy(s => s.Name).ToListAsync();
        }

        public async Task<Site?> GetByIdAsync(string id)
        {
            if (_context.Sites == null)
                return null;

            return await _context.Sites
                .Include(s => s.Devices)
                .Include(s => s.Network)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<Site> CreateAsync(Site site)
        {
            if (_context.Sites == null)
                throw new InvalidOperationException("DbSet<Site> is null");

            site.Id = Guid.NewGuid().ToString();
            site.CreatedAt = DateTime.Now;
            _context.Sites.Add(site);
            await _context.SaveChangesAsync();
            return site;
        }

        public async Task<Site> UpdateAsync(Site site)
        {
            if (_context.Sites == null)
                throw new InvalidOperationException("DbSet<Site> is null");

            _context.Sites.Update(site);
            await _context.SaveChangesAsync();
            return site;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            if (_context.Sites == null)
                throw new InvalidOperationException("DbSet<Site> is null");

            var site = await _context.Sites.FindAsync(id);
            if (site == null) return false;

            _context.Sites.Remove(site);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Site>> SearchAsync(string searchTerm, string? networkFilter = null)
        {
            var query = _context.Sites?.Include(s => s.Devices).Include(s => s.Network).AsQueryable() ?? 
                throw new InvalidOperationException("DbSet<Site> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(s => s.NetworkId == networkFilter);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(s =>
                    s.Name.Contains(searchTerm) ||
                    (s.Address ?? "").Contains(searchTerm) ||
                    (s.Phone ?? "").Contains(searchTerm));
            }

            return await query.OrderBy(s => s.Name).ToListAsync();
        }
    }
}

