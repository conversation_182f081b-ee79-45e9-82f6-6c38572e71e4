<Window x:Class="NetworkManagement.Views.UserDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding Title}"
        Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        Foreground="{DynamicResource MaterialDesignBody}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Username -->
                <TextBox materialDesign:HintAssist.Hint="اسم المستخدم *"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                         IsEnabled="{Binding IsEditMode, Converter={StaticResource InverseBooleanConverter}}"
                         Margin="0,0,0,20"/>

                <!-- Full Name -->
                <TextBox materialDesign:HintAssist.Hint="الاسم الكامل *"
                         materialDesign:HintAssist.IsFloating="True"
                         Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"/>

                <!-- Role -->
                <ComboBox materialDesign:HintAssist.Hint="الدور *"
                          materialDesign:HintAssist.IsFloating="True"
                          ItemsSource="{Binding Roles}"
                          SelectedItem="{Binding Role}"
                          Margin="0,0,0,20"/>

                <!-- Network -->
                <ComboBox materialDesign:HintAssist.Hint="الشبكة (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding AvailableNetworks}"
                         SelectedValuePath="Id"
                         DisplayMemberPath="Name"
                         SelectedValue="{Binding SelectedNetworkId}"
                         Margin="0,0,0,20"/>

                <!-- Password Section -->
                <StackPanel Visibility="{Binding ShowPasswordFields, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <!-- Password -->
                    <PasswordBox materialDesign:HintAssist.Hint="كلمة المرور *"
                                 materialDesign:HintAssist.IsFloating="True"
                                 x:Name="PasswordBox"
                                 PasswordChanged="PasswordBox_PasswordChanged"
                                 Margin="0,0,0,20"/>

                    <!-- Confirm Password -->
                    <PasswordBox materialDesign:HintAssist.Hint="تأكيد كلمة المرور *"
                                 materialDesign:HintAssist.IsFloating="True"
                                 x:Name="ConfirmPasswordBox"
                                 PasswordChanged="ConfirmPasswordBox_PasswordChanged"
                                 Margin="0,0,0,20"/>
                </StackPanel>

                <!-- Password Section for Edit Mode -->
                <StackPanel Visibility="{Binding ShowPasswordFields, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <TextBlock Text="تغيير كلمة المرور (اختياري):"
                               FontWeight="Medium"
                               Margin="0,0,0,10"/>

                    <!-- New Password -->
                    <PasswordBox materialDesign:HintAssist.Hint="كلمة المرور الجديدة"
                                 materialDesign:HintAssist.IsFloating="True"
                                 x:Name="NewPasswordBox"
                                 PasswordChanged="NewPasswordBox_PasswordChanged"
                                 Margin="0,0,0,20"/>

                    <!-- Confirm New Password -->
                    <PasswordBox materialDesign:HintAssist.Hint="تأكيد كلمة المرور الجديدة"
                                 materialDesign:HintAssist.IsFloating="True"
                                 x:Name="ConfirmNewPasswordBox"
                                 PasswordChanged="ConfirmNewPasswordBox_PasswordChanged"
                                 Margin="0,0,0,20"/>
                </StackPanel>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                           Foreground="{DynamicResource ValidationErrorBrush}"
                           FontWeight="Medium"
                           Margin="0,0,0,10"
                           Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Required Fields Note -->
                <TextBlock Text="* الحقول المطلوبة"
                           FontSize="12"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           Margin="0,10,0,0"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="1"
                Background="{DynamicResource MaterialDesignPaper}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="0,1,0,0"
                Padding="20">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Column="0"
                             IsIndeterminate="True"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                             VerticalAlignment="Center"
                             Height="4"
                             Margin="0,0,20,0"/>

                <!-- Cancel Button -->
                <Button Grid.Column="1"
                        Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,10,0"
                        MinWidth="80"/>

                <!-- Save Button -->
                <Button Grid.Column="2"
                        Content="{Binding IsEditMode, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='تحديث|حفظ'}"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                        MinWidth="80"/>
            </Grid>
        </Border>
    </Grid>
</Window>
