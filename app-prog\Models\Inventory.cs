using System;
using System.ComponentModel.DataAnnotations;

namespace NetworkManagement.Models
{
    public class Inventory
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = string.Empty;

        [Required]
        public int Quantity { get; set; } = 0;

        [Required]
        [MaxLength(20)]
        public string Unit { get; set; } = string.Empty;

        public DateTime? LastUpdated { get; set; } = DateTime.Now;

        [MaxLength(500)]
        public string? Description { get; set; }

        public decimal? UnitPrice { get; set; }

        [MaxLength(100)]
        public string? Location { get; set; }

        public int? MinimumStock { get; set; }

        public int? MaximumStock { get; set; }

        [MaxLength(50)]
        public string? Supplier { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
