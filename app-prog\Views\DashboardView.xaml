<UserControl x:Class="NetworkManagement.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel>
            <!-- Page Header with Refresh Button -->
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="لوحة التحكم" Style="{StaticResource PageHeaderStyle}"/>

                <Button Grid.Column="1"
                       Command="{Binding RefreshDataCommand}"
                       Style="{StaticResource MaterialDesignIconButton}"
                       ToolTip="تحديث البيانات">
                    <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24"/>
                </Button>
            </Grid>

            <!-- Loading Indicator -->
            <ProgressBar IsIndeterminate="True"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Margin="0,0,0,20"/>

            <!-- Statistics Cards -->
            <Grid Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Devices Card -->
                <materialDesign:Card Grid.Row="0" Grid.Column="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="Router"
                                                   Width="24" Height="24"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إجمالي الأجهزة"
                                      FontSize="16" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalDevices}"
                                  FontSize="32" FontWeight="Bold"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="{Binding ActiveDevices, StringFormat='منها {0} نشط'}"
                                  FontSize="12"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Sites Card -->
                <materialDesign:Card Grid.Row="0" Grid.Column="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="MapMarker"
                                                   Width="24" Height="24"
                                                   Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إجمالي المواقع"
                                      FontSize="16" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalSites}"
                                  FontSize="32" FontWeight="Bold"
                                  Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Pending Tasks Card -->
                <materialDesign:Card Grid.Row="0" Grid.Column="2" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="ClipboardList"
                                                   Width="24" Height="24"
                                                   Foreground="#FF9800"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="المهام المعلقة"
                                      FontSize="16" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding PendingTasks}"
                                  FontSize="32" FontWeight="Bold"
                                  Foreground="#FF9800"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Monthly Spending Card -->
                <materialDesign:Card Grid.Row="1" Grid.Column="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="CurrencyUsd"
                                                   Width="24" Height="24"
                                                   Foreground="#4CAF50"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إنفاق الشهر"
                                      FontSize="16" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding MonthlySpending, StringFormat='{}{0:C}'}"
                                  FontSize="32" FontWeight="Bold"
                                  Foreground="#4CAF50"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Low Stock Items Card -->
                <materialDesign:Card Grid.Row="1" Grid.Column="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                   Width="24" Height="24"
                                                   Foreground="#F44336"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="مخزون منخفض"
                                      FontSize="16" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding LowStockItems}"
                                  FontSize="32" FontWeight="Bold"
                                  Foreground="#F44336"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Quick Actions Card -->
                <materialDesign:Card Grid.Row="1" Grid.Column="2" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="إجراءات سريعة"
                                  FontSize="16" FontWeight="Medium"
                                  Margin="0,0,0,15"/>

                        <Button Content="إضافة جهاز جديد"
                               Command="{Binding AddNewDeviceCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="0,0,0,5" HorizontalAlignment="Stretch"
                               Visibility="{Binding CanAddDevices, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                        <Button Content="إضافة موقع جديد"
                               Command="{Binding AddNewSiteCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="0,0,0,5" HorizontalAlignment="Stretch"
                               Visibility="{Binding CanAddSites, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                        <Button Content="عرض التقارير"
                               Command="{Binding ViewReportsCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               HorizontalAlignment="Stretch"
                               Visibility="{Binding CanViewReports, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </StackPanel>
    </ScrollViewer>
</UserControl>
