<UserControl x:Class="NetworkManagement.Views.DatabaseSetupView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Background="{DynamicResource MaterialDesignPaper}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="20,20,20,10" Padding="20">
            <StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                    <materialDesign:PackIcon Kind="Database" Width="32" Height="32" Margin="0,0,10,0" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إعداد قاعدة البيانات" Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </StackPanel>
                <TextBlock Text="يرجى إعداد اتصال قاعدة البيانات MySQL قبل المتابعة" 
                          Style="{StaticResource MaterialDesignBody1TextBlock}"
                          HorizontalAlignment="Center" Opacity="0.7"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20,0,20,20" MaxWidth="600">

                <!-- Connection Settings -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="إعدادات الاتصال" Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                  Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Server -->
                            <TextBox Grid.Row="0" Grid.Column="0" 
                                    materialDesign:HintAssist.Hint="عنوان الخادم"
                                    Text="{Binding MySQLServer, UpdateSourceTrigger=PropertyChanged}"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Margin="0,0,0,15"/>

                            <!-- Port -->
                            <TextBox Grid.Row="0" Grid.Column="2"
                                    materialDesign:HintAssist.Hint="المنفذ"
                                    Text="{Binding MySQLPort, UpdateSourceTrigger=PropertyChanged}"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Margin="0,0,0,15"/>

                            <!-- Database Name -->
                            <TextBox Grid.Row="1" Grid.Column="0"
                                    materialDesign:HintAssist.Hint="اسم قاعدة البيانات"
                                    Text="{Binding MySQLDatabase, UpdateSourceTrigger=PropertyChanged}"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Margin="0,0,0,15"/>

                            <!-- Username -->
                            <TextBox Grid.Row="2" Grid.Column="0"
                                    materialDesign:HintAssist.Hint="اسم المستخدم"
                                    Text="{Binding MySQLUser, UpdateSourceTrigger=PropertyChanged}"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Margin="0,0,0,15"/>

                            <!-- Password -->
                            <PasswordBox Grid.Row="2" Grid.Column="2"
                                        materialDesign:HintAssist.Hint="كلمة المرور"
                                        materialDesign:PasswordBoxAssist.Password="{Binding MySQLPassword, UpdateSourceTrigger=PropertyChanged}"
                                        Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                        Margin="0,0,0,15"/>
                        </Grid>

                        <!-- Connection Status -->
                        <Border Background="{Binding IsDatabaseConnected, Converter={StaticResource BooleanToColorConverter}, ConverterParameter='LightGreen,LightCoral'}"
                               CornerRadius="5" Padding="10" Margin="0,0,0,15">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="{Binding IsDatabaseConnected, Converter={StaticResource BooleanToStringConverter}, ConverterParameter=CheckCircle|AlertCircle}" 
                                                        Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding DatabaseConnectionStatus}" FontWeight="Medium"/>
                            </StackPanel>
                        </Border>

                        <!-- Test Connection Button -->
                        <Button Command="{Binding TestConnectionCommand}"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               IsEnabled="{Binding IsNotLoading}"
                               Margin="0,0,0,10">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="DatabaseSync" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="اختبار الاتصال"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Database Operations -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="عمليات قاعدة البيانات" Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                  Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Create Database -->
                            <Button Grid.Row="0" Grid.Column="0"
                                   Command="{Binding CreateDatabaseCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   IsEnabled="{Binding IsNotLoading}"
                                   Margin="0,0,0,10">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabasePlus" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="إنشاء قاعدة البيانات"/>
                                </StackPanel>
                            </Button>

                            <!-- Import Database -->
                            <Button Grid.Row="0" Grid.Column="2"
                                   Command="{Binding ImportDatabaseCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   IsEnabled="{Binding IsNotLoading}"
                                   Margin="0,0,0,10">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabaseImport" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="استيراد قاعدة البيانات"/>
                                </StackPanel>
                            </Button>

                            <!-- Backup Database -->
                            <Button Grid.Row="1" Grid.Column="0"
                                   Command="{Binding BackupDatabaseCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   IsEnabled="{Binding CanBackupDatabase}"
                                   Margin="0,0,0,10">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabaseExport" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="نسخ احتياطي"/>
                                </StackPanel>
                            </Button>

                            <!-- Restore Database -->
                            <Button Grid.Row="1" Grid.Column="2"
                                   Command="{Binding RestoreDatabaseCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   IsEnabled="{Binding IsNotLoading}"
                                   Margin="0,0,0,10">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabaseRefresh" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="استعادة من نسخة احتياطية"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <!-- Delete Database Section -->
                        <Separator Margin="0,10,0,15"/>
                        <TextBlock Text="عمليات خطيرة" Style="{StaticResource MaterialDesignBody2TextBlock}"
                                  Foreground="Red" FontWeight="Bold" Margin="0,0,0,10"/>

                        <Button Command="{Binding DeleteDatabaseCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               BorderBrush="Red" Foreground="Red"
                               IsEnabled="{Binding CanDeleteDatabase}"
                               Margin="0,0,0,10">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="DatabaseRemove" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="حذف قاعدة البيانات"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Progress -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <ProgressBar IsIndeterminate="True" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding ProgressMessage}" HorizontalAlignment="Center"/>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <materialDesign:Card Grid.Row="2" Margin="20,10,20,20" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Command="{Binding ContinueToLoginCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       IsEnabled="{Binding IsDatabaseConnected}"
                       Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Login" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="المتابعة لتسجيل الدخول"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding ExitApplicationCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ExitToApp" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إغلاق التطبيق"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>

    </Grid>
</UserControl>
