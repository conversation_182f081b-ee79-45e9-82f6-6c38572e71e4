<UserControl x:Class="NetworkManagement.Views.SitesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <TextBlock Grid.Row="0" Text="إدارة المواقع" Style="{StaticResource PageHeaderStyle}"/>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search -->
                <TextBox Grid.Column="0"
                        materialDesign:HintAssist.Hint="البحث في المواقع..."
                        materialDesign:HintAssist.IsFloating="False"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        MaxWidth="300" HorizontalAlignment="Left"
                        Margin="0,0,10,0">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchSitesCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Command="{Binding SearchSitesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="بحث"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding LoadSitesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تحديث"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding AddSiteCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Visibility="{Binding CanAddSites, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة موقع"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Sites List -->
        <materialDesign:Card Grid.Row="2">
            <Grid>
                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                            VerticalAlignment="Top" Height="4"/>

                <!-- Data Grid -->
                <DataGrid ItemsSource="{Binding Sites}"
                         SelectedItem="{Binding SelectedSite}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         SelectionMode="Single"
                         Margin="10">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الموقع" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="200"/>
                        <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                        <DataGridTextColumn Header="مصدر الطاقة" Binding="{Binding PowerSource}" Width="120"/>
                        <DataGridTextColumn Header="سعة التخزين" Binding="{Binding StorageCapacity}" Width="100"/>
                        <DataGridTextColumn Header="الاستهلاك اليومي" Binding="{Binding DailyConsumption}" Width="120"/>
                        <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="100"/>
                        <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=dd/MM/yyyy}" Width="120"/>

                        <!-- Actions Column -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="تحرير"
                                               Command="{Binding DataContext.EditSiteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               Style="{StaticResource MaterialDesignFlatButton}"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               Margin="0,0,5,0"
                                               Visibility="{Binding Converter={StaticResource CanEditDataConverter}}"/>
                                        <Button Content="حذف"
                                               Command="{Binding DataContext.DeleteSiteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               Style="{StaticResource MaterialDesignFlatButton}"
                                               Foreground="Red"
                                               Visibility="{Binding Converter={StaticResource CanDeleteDataConverter}}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Empty State -->
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center"
                           Visibility="{Binding Sites.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                    <materialDesign:PackIcon Kind="MapMarker" Width="64" Height="64"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <TextBlock Text="لا توجد مواقع"
                              FontSize="16"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    <TextBlock Text="انقر على 'إضافة موقع' لإضافة الموقع الأول"
                              FontSize="12"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
