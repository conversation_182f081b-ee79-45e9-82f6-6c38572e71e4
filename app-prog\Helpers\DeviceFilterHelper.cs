using System;
using System.Collections.Generic;
using System.Linq;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.Helpers
{
    /// <summary>
    /// فئة مساعدة لتوحيد منطق فلترة الأجهزة وإزالة التكرار
    /// </summary>
    public static class DeviceFilterHelper
    {
        /// <summary>
        /// تطبيق جميع الفلاتر على قائمة الأجهزة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <param name="searchText">نص البحث</param>
        /// <param name="statusFilter">فلتر الحالة</param>
        /// <param name="typeFilter">فلتر النوع</param>
        /// <param name="authService">خدمة المصادقة للصلاحيات</param>
        /// <returns>الأجهزة المفلترة</returns>
        public static IEnumerable<Device> ApplyAllFilters(
            IEnumerable<Device> devices,
            string? searchText = null,
            string? statusFilter = null,
            string? typeFilter = null,
            IAuthService? authService = null)
        {
            var filteredDevices = devices;

            // تطبيق فلترة الصلاحيات
            if (authService != null)
            {
                filteredDevices = PermissionHelper.ApplyPermissionFilter(
                    filteredDevices, authService, device => device.NetworkId);
            }

            // تطبيق فلتر الحالة
            if (!string.IsNullOrEmpty(statusFilter))
            {
                filteredDevices = filteredDevices.Where(d => d.Status == statusFilter);
            }

            // تطبيق فلتر النوع
            if (!string.IsNullOrEmpty(typeFilter))
            {
                filteredDevices = filteredDevices.Where(d => d.Type == typeFilter);
            }

            // تطبيق فلتر البحث النصي
            if (!string.IsNullOrEmpty(searchText))
            {
                filteredDevices = ApplySearchFilter(filteredDevices, searchText);
            }

            return filteredDevices.OrderBy(d => d.Location);
        }

        /// <summary>
        /// تطبيق فلتر البحث النصي على الأجهزة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <param name="searchText">نص البحث</param>
        /// <returns>الأجهزة المطابقة للبحث</returns>
        public static IEnumerable<Device> ApplySearchFilter(IEnumerable<Device> devices, string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return devices;

            var searchLower = searchText.ToLowerInvariant();

            return devices.Where(d =>
                (d.Responsible?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (d.Location?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (d.Type?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (d.Ip?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (d.Phone?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (d.ConnectionMethod?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (d.LinkedNetwork?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false));
        }

        /// <summary>
        /// تطبيق فلتر الحالة على الأجهزة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <param name="status">الحالة المطلوبة</param>
        /// <returns>الأجهزة بالحالة المحددة</returns>
        public static IEnumerable<Device> ApplyStatusFilter(IEnumerable<Device> devices, string status)
        {
            if (string.IsNullOrWhiteSpace(status))
                return devices;

            return devices.Where(d => d.Status == status);
        }

        /// <summary>
        /// تطبيق فلتر النوع على الأجهزة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <param name="type">النوع المطلوب</param>
        /// <returns>الأجهزة من النوع المحدد</returns>
        public static IEnumerable<Device> ApplyTypeFilter(IEnumerable<Device> devices, string type)
        {
            if (string.IsNullOrWhiteSpace(type))
                return devices;

            return devices.Where(d => d.Type == type);
        }

        /// <summary>
        /// الحصول على الأجهزة التي تحتوي على عناوين IP صالحة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <returns>الأجهزة التي لها عناوين IP</returns>
        public static IEnumerable<Device> GetDevicesWithValidIp(IEnumerable<Device> devices)
        {
            return devices.Where(d => !string.IsNullOrWhiteSpace(d.Ip));
        }

        /// <summary>
        /// الحصول على الأجهزة المحددة من قائمة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <returns>الأجهزة المحددة</returns>
        public static IEnumerable<Device> GetSelectedDevices(IEnumerable<Device> devices)
        {
            return devices.Where(d => d.IsSelected);
        }

        /// <summary>
        /// الحصول على الأجهزة المحددة التي لها عناوين IP صالحة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <returns>الأجهزة المحددة التي لها عناوين IP</returns>
        public static IEnumerable<Device> GetSelectedDevicesWithValidIp(IEnumerable<Device> devices)
        {
            return devices.Where(d => d.IsSelected && !string.IsNullOrWhiteSpace(d.Ip));
        }

        /// <summary>
        /// تجميع الأجهزة حسب الحالة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <returns>قاموس يحتوي على عدد الأجهزة لكل حالة</returns>
        public static Dictionary<string, int> GroupDevicesByStatus(IEnumerable<Device> devices)
        {
            return devices
                .GroupBy(d => d.Status ?? "غير محدد")
                .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// تجميع الأجهزة حسب النوع
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <returns>قاموس يحتوي على عدد الأجهزة لكل نوع</returns>
        public static Dictionary<string, int> GroupDevicesByType(IEnumerable<Device> devices)
        {
            return devices
                .GroupBy(d => d.Type ?? "غير محدد")
                .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// تجميع الأجهزة حسب الشبكة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <returns>قاموس يحتوي على عدد الأجهزة لكل شبكة</returns>
        public static Dictionary<string, int> GroupDevicesByNetwork(IEnumerable<Device> devices)
        {
            return devices
                .GroupBy(d => d.Network?.Name ?? "غير محدد")
                .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// الحصول على إحصائيات سريعة للأجهزة
        /// </summary>
        /// <param name="devices">قائمة الأجهزة</param>
        /// <returns>إحصائيات الأجهزة</returns>
        public static DeviceStatistics GetDeviceStatistics(IEnumerable<Device> devices)
        {
            var deviceList = devices.ToList();
            
            return new DeviceStatistics
            {
                TotalDevices = deviceList.Count,
                ActiveDevices = deviceList.Count(d => d.Status == "active" || d.Status == "متصل"),
                InactiveDevices = deviceList.Count(d => d.Status == "inactive" || d.Status == "غير متصل"),
                MaintenanceDevices = deviceList.Count(d => d.Status == "maintenance" || d.Status == "صيانة"),
                DevicesWithIp = deviceList.Count(d => !string.IsNullOrWhiteSpace(d.Ip)),
                SelectedDevices = deviceList.Count(d => d.IsSelected),
                StatusGroups = GroupDevicesByStatus(deviceList),
                TypeGroups = GroupDevicesByType(deviceList),
                NetworkGroups = GroupDevicesByNetwork(deviceList)
            };
        }
    }

    /// <summary>
    /// فئة لحفظ إحصائيات الأجهزة
    /// </summary>
    public class DeviceStatistics
    {
        public int TotalDevices { get; set; }
        public int ActiveDevices { get; set; }
        public int InactiveDevices { get; set; }
        public int MaintenanceDevices { get; set; }
        public int DevicesWithIp { get; set; }
        public int SelectedDevices { get; set; }
        public Dictionary<string, int> StatusGroups { get; set; } = new();
        public Dictionary<string, int> TypeGroups { get; set; } = new();
        public Dictionary<string, int> NetworkGroups { get; set; } = new();
    }
}
