using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.ViewModels
{
    public partial class PurchaseDialogViewModel : ObservableObject
    {
        private readonly IPurchaseService _purchaseService;
        private readonly IAuthService _authService;
        private readonly INetworkService _networkService;

        [ObservableProperty]
        private string title = "إضافة مشترى جديد";

        [ObservableProperty]
        private string itemType = string.Empty;

        [ObservableProperty]
        private decimal price;

        [ObservableProperty]
        private int? quantity = 1;

        [ObservableProperty]
        private string unit = "قطعة";

        [ObservableProperty]
        private string supplier = string.Empty;

        [ObservableProperty]
        private string category = "أجهزة";

        [ObservableProperty]
        private DateTime date = DateTime.Now;

        [ObservableProperty]
        private string invoiceNumber = string.Empty;

        [ObservableProperty]
        private string description = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private string selectedNetworkId = string.Empty;

        [ObservableProperty]
        private List<Network> availableNetworks = new();

        public string[] Categories { get; } = { "أجهزة", "كابلات", "أدوات", "صيانة", "أخرى" };
        public string[] Units { get; } = { "قطعة", "متر", "كيلو", "لتر", "عبوة", "صندوق" };

        private Purchase? _editingPurchase;
        public bool IsEditMode => _editingPurchase != null;

        public event EventHandler<Purchase>? PurchaseSaved;
        public event EventHandler? DialogClosed;

        public PurchaseDialogViewModel(IPurchaseService purchaseService, IAuthService authService, INetworkService networkService)
        {
            _purchaseService = purchaseService;
            _authService = authService;
            _networkService = networkService;
            _ = LoadNetworksAsync();
        }

        public void SetEditPurchase(Purchase purchase)
        {
            _editingPurchase = purchase;
            Title = "تحرير المشترى";

            ItemType = purchase.ItemType;
            Price = purchase.Price;
            Quantity = purchase.Quantity ?? 1;
            Unit = purchase.Unit ?? "قطعة";
            Supplier = purchase.Supplier ?? string.Empty;
            Category = purchase.Category ?? "أجهزة";
            Date = purchase.Date;
            InvoiceNumber = purchase.InvoiceNumber ?? string.Empty;
            Description = purchase.Description ?? string.Empty;
            SelectedNetworkId = purchase.NetworkId ?? string.Empty;
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SaveAsync()
        {
            try
            {
                if (!ValidateInput())
                    return;

                IsLoading = true;
                ErrorMessage = string.Empty;

                var purchase = _editingPurchase ?? new Purchase();

                purchase.ItemType = ItemType.Trim();
                purchase.Price = Price;
                purchase.Quantity = Quantity;
                purchase.Unit = string.IsNullOrWhiteSpace(Unit) ? null : Unit.Trim();
                purchase.Supplier = string.IsNullOrWhiteSpace(Supplier) ? null : Supplier.Trim();
                purchase.Category = Category;
                purchase.Date = Date;
                purchase.InvoiceNumber = string.IsNullOrWhiteSpace(InvoiceNumber) ? null : InvoiceNumber.Trim();
                purchase.Description = string.IsNullOrWhiteSpace(Description) ? null : Description.Trim();

                // تعيين NetworkId - تعيين شبكة المستخدم تلقائ<|im_start|> للـ Network Manager
                if (!string.IsNullOrWhiteSpace(SelectedNetworkId))
                {
                    // استخدام الشبكة المختارة
                    purchase.NetworkId = SelectedNetworkId;
                }
                else
                {
                    // إذا كان المستخدم Network Manager، استخدم شبكته تلقائ<|im_start|>
                    var currentUser = _authService.CurrentUser;
                    if (_authService.IsAdmin)
                    {
                        // للأدمن: يمكن ترك الحقل فارغ
                        purchase.NetworkId = null;
                    }
                    else if (_authService.IsNetworkManager && !string.IsNullOrEmpty(currentUser?.NetworkId))
                    {
                        // للـ Network Manager: استخدم شبكته تلقائياً
                        purchase.NetworkId = currentUser.NetworkId;
                    }
                    else
                    {
                        // لباقي المستخدمين: يمكن ترك الحقل فارغ
                        purchase.NetworkId = null;
                    }
                }

                if (_editingPurchase == null)
                {
                    // Adding new purchase
                    await _purchaseService.CreateAsync(purchase);
                }
                else
                {
                    // Updating existing purchase
                    await _purchaseService.UpdateAsync(purchase);
                }

                PurchaseSaved?.Invoke(this, purchase);
                DialogClosed?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ المشترى: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }

        private bool ValidateInput()
        {
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(ItemType))
            {
                ErrorMessage = "نوع العنصر مطلوب";
                return false;
            }

            if (Price <= 0)
            {
                ErrorMessage = "السعر يجب أن يكون أكبر من صفر";
                return false;
            }

            if (!Quantity.HasValue || Quantity <= 0)
            {
                ErrorMessage = "الكمية يجب أن تكون أكبر من صفر";
                return false;
            }

            return true;
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                // استخدام NetworkHelper لتحميل الشبكات مع تطبيق الصلاحيات
                var (networks, defaultNetworkId) = await NetworkHelper.LoadNetworksForSelectionAsync(
                    _authService,
                    _networkService);

                AvailableNetworks = networks;

                // تعيين الشبكة الافتراضية
                if (!string.IsNullOrEmpty(defaultNetworkId))
                {
                    SelectedNetworkId = defaultNetworkId;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks: {ex.Message}");
            }
        }
    }
}
