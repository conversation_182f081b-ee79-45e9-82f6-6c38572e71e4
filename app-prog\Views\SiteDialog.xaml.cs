using System.Windows;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class SiteDialog : Window
    {
        public SiteDialog()
        {
            InitializeComponent();
        }

        public SiteDialog(SiteDialogViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // Subscribe to events
            viewModel.DialogClosed += (s, e) => Close();
        }
    }
}
