<Window x:Class="NetworkManagement.Views.DatabaseSetupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إعداد قاعدة البيانات - Shabaka Pro"
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        Icon="pack://application:,,,/Resources/shabaka-pro.ico"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <views:DatabaseSetupView xmlns:views="clr-namespace:NetworkManagement.Views"
                                DataContext="{Binding}"/>
    </Grid>
</Window>
