-- تحديث بنية قاعدة البيانات لجعل NetworkId اختياري
-- يج<PERSON> تشغيل هذا الملف على قاعدة البيانات الموجودة

USE NetworkManagementDB;

-- ت<PERSON>د<PERSON><PERSON> جدول المواقع - جعل NetworkId اختياري
ALTER TABLE Sites MODIFY COLUMN NetworkId VARCHAR(50) NULL;

-- تحديث جدول المستخدمين - جعل NetworkId اختياري (هو كذلك بالفعل)
ALTER TABLE Users MODIFY COLUMN NetworkId VARCHAR(50) NULL;

-- تحدي<PERSON> جدول الأجهزة - جعل NetworkId و SiteId اختياري
ALTER TABLE Devices MODIFY COLUMN NetworkId VARCHAR(50) NULL;
ALTER TABLE Devices MODIFY COLUMN SiteId VARCHAR(50) NULL;

-- تحديث جدول المهام - جعل NetworkId اختياري
ALTER TABLE Tasks MODIFY COLUMN NetworkId VARCHAR(50) NULL;

-- تحديث جدول المشتريات - جعل NetworkId اختياري
ALTER TABLE Purchases MODIFY COLUMN NetworkId VARCHAR(50) NULL;

-- تحديث جدول المخزون - جعل NetworkId اختياري
ALTER TABLE Inventory MODIFY COLUMN NetworkId VARCHAR(50) NULL;

-- إضافة أعمدة مفقودة في جداول مختلفة إذا لم تكن موجودة

-- تحديث جدول المواقع
ALTER TABLE Sites
ADD COLUMN IF NOT EXISTS Phone VARCHAR(20) AFTER Address,
ADD COLUMN IF NOT EXISTS GpsLat DOUBLE AFTER Phone,
ADD COLUMN IF NOT EXISTS GpsLng DOUBLE AFTER GpsLat,
ADD COLUMN IF NOT EXISTS PowerSource VARCHAR(100) AFTER GpsLng,
ADD COLUMN IF NOT EXISTS InstallationBase VARCHAR(100) AFTER DailyConsumption,
ADD COLUMN IF NOT EXISTS Boxes INT AFTER InstallationBase,
ADD COLUMN IF NOT EXISTS WireLength INT AFTER Boxes,
ADD COLUMN IF NOT EXISTS AssociatedDeviceIds TEXT AFTER WireLength;

-- تحديث أنواع البيانات في جدول المواقع
ALTER TABLE Sites
MODIFY COLUMN Address VARCHAR(200),
MODIFY COLUMN StorageCapacity INT,
MODIFY COLUMN DailyConsumption INT;

-- إزالة الأعمدة القديمة من جدول المواقع إذا كانت موجودة
ALTER TABLE Sites
DROP COLUMN IF EXISTS Latitude,
DROP COLUMN IF EXISTS Longitude,
DROP COLUMN IF EXISTS PowerConsumption,
DROP COLUMN IF EXISTS CableLength,
DROP COLUMN IF EXISTS InstallationDate,
DROP COLUMN IF EXISTS Infrastructure;

-- تحديث جدول الأجهزة
ALTER TABLE Devices
ADD COLUMN IF NOT EXISTS Phone VARCHAR(20) AFTER Location,
ADD COLUMN IF NOT EXISTS PowerConnection VARCHAR(100) AFTER InstallDate,
ADD COLUMN IF NOT EXISTS AdapterType VARCHAR(50) AFTER PowerConnection,
ADD COLUMN IF NOT EXISTS BroadcastNetworkName VARCHAR(100) AFTER LinkedNetwork;

-- تحديث جدول المهام
ALTER TABLE Tasks
ADD COLUMN IF NOT EXISTS Date DATETIME NOT NULL DEFAULT NOW() AFTER Description,
ADD COLUMN IF NOT EXISTS Notes TEXT AFTER Status,
ADD COLUMN IF NOT EXISTS CompletedAt DATETIME AFTER DueDate;

-- إزالة عمود Title من جدول المهام إذا كان موجوداً
ALTER TABLE Tasks DROP COLUMN IF EXISTS Title;

-- تحديث جدول المشتريات
ALTER TABLE Purchases
ADD COLUMN IF NOT EXISTS Unit VARCHAR(50) AFTER Quantity,
ADD COLUMN IF NOT EXISTS InvoiceNumber VARCHAR(100) AFTER Supplier;

-- تحديث نوع البيانات في جدول المشتريات
ALTER TABLE Purchases MODIFY COLUMN Price DECIMAL(10,2);

-- تحديث جدول المخزون
ALTER TABLE Inventory
ADD COLUMN IF NOT EXISTS UnitPrice DECIMAL(10,2) AFTER Unit,
ADD COLUMN IF NOT EXISTS MaximumStock INT AFTER MinimumStock,
ADD COLUMN IF NOT EXISTS Category VARCHAR(100) AFTER MaximumStock,
ADD COLUMN IF NOT EXISTS LastUpdated DATETIME DEFAULT NOW() AFTER Category;

-- تحديث البيانات الموجودة لإزالة القيود
UPDATE Sites SET NetworkId = NULL WHERE NetworkId = '';
UPDATE Tasks SET NetworkId = NULL WHERE NetworkId = '';
UPDATE Purchases SET NetworkId = NULL WHERE NetworkId = '';
UPDATE Inventory SET NetworkId = NULL WHERE NetworkId = '';
UPDATE Devices SET NetworkId = NULL WHERE NetworkId = '';
UPDATE Devices SET SiteId = NULL WHERE SiteId = '';

-- إزالة الفهرس الفريد على IP من جدول الأجهزة لتجنب مشاكل القيم الفارغة
DROP INDEX IF EXISTS IX_Devices_Ip ON Devices;

-- إنشاء فهرس عادي (غير فريد) على IP
CREATE INDEX IF NOT EXISTS IX_Devices_Ip ON Devices(Ip);

SELECT 'تم تحديث بنية قاعدة البيانات بنجاح!' as Result;
