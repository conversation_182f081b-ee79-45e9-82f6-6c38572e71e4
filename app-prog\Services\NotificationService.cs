using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace NetworkManagement.Services
{
    public class NotificationService : INotificationService
    {
        private bool _notificationsEnabled = true;
        private readonly DispatcherTimer _cleanupTimer;
        
        public ObservableCollection<NotificationItem> Notifications { get; } = new();

        public NotificationService()
        {
            // Timer to cleanup old notifications
            _cleanupTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(1)
            };
            _cleanupTimer.Tick += CleanupOldNotifications;
            _cleanupTimer.Start();
        }

        public async Task ShowInfoAsync(string title, string message)
        {
            await ShowCustomAsync(title, message, NotificationType.Info);
        }

        public async Task ShowWarningAsync(string title, string message)
        {
            await ShowCustomAsync(title, message, NotificationType.Warning);
        }

        public async Task ShowErrorAsync(string title, string message)
        {
            await ShowCustomAsync(title, message, NotificationType.Error);
        }

        public async Task ShowSuccessAsync(string title, string message)
        {
            await ShowCustomAsync(title, message, NotificationType.Success);
        }

        public async Task ShowCustomAsync(string title, string message, NotificationType type, TimeSpan? duration = null)
        {
            if (!_notificationsEnabled) return;

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var notification = new NotificationItem
                {
                    Id = Guid.NewGuid(),
                    Title = title,
                    Message = message,
                    Type = type,
                    Timestamp = DateTime.Now,
                    Duration = duration ?? GetDefaultDuration(type)
                };

                Notifications.Insert(0, notification);

                // Keep only last 10 notifications
                while (Notifications.Count > 10)
                {
                    Notifications.RemoveAt(Notifications.Count - 1);
                }

                // Show system notification if available
                ShowSystemNotification(title, message, type);
            });
        }

        public async Task ShowDeviceStatusAsync(string deviceName, string status, bool isOnline)
        {
            var title = isOnline ? "جهاز متصل" : "جهاز غير متصل";
            var message = $"{deviceName}: {status}";
            var type = isOnline ? NotificationType.Success : NotificationType.Warning;
            
            await ShowCustomAsync(title, message, type);
        }

        public async Task ShowLowStockAsync(string itemName, int currentStock, int minimumStock)
        {
            var title = "تحذير: مخزون منخفض";
            var message = $"{itemName}: الكمية الحالية {currentStock} أقل من الحد الأدنى {minimumStock}";
            
            await ShowCustomAsync(title, message, NotificationType.Warning, TimeSpan.FromMinutes(5));
        }

        public async Task ShowTaskCompletedAsync(string taskTitle)
        {
            var title = "مهمة مكتملة";
            var message = $"تم إكمال المهمة: {taskTitle}";
            
            await ShowCustomAsync(title, message, NotificationType.Success);
        }

        public async Task ShowBackupNotificationAsync(bool success, string message)
        {
            var title = success ? "نسخة احتياطية ناجحة" : "فشل في النسخة الاحتياطية";
            var type = success ? NotificationType.Success : NotificationType.Error;
            
            await ShowCustomAsync(title, message, type, TimeSpan.FromMinutes(3));
        }

        public void ClearAllNotifications()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                Notifications.Clear();
            });
        }

        public void SetNotificationsEnabled(bool enabled)
        {
            _notificationsEnabled = enabled;
        }

        private TimeSpan GetDefaultDuration(NotificationType type)
        {
            return type switch
            {
                NotificationType.Error => TimeSpan.FromMinutes(5),
                NotificationType.Warning => TimeSpan.FromMinutes(3),
                NotificationType.Success => TimeSpan.FromMinutes(2),
                NotificationType.Info => TimeSpan.FromMinutes(1),
                _ => TimeSpan.FromMinutes(2)
            };
        }

        private void ShowSystemNotification(string title, string message, NotificationType type)
        {
            try
            {
                // For now, we'll use MessageBox for critical errors only
                if (type == NotificationType.Error)
                {
                    // Could implement Windows Toast notifications here
                    // For now, just log to debug
                    System.Diagnostics.Debug.WriteLine($"[{type}] {title}: {message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing system notification: {ex.Message}");
            }
        }

        private void CleanupOldNotifications(object? sender, EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var cutoffTime = DateTime.Now;
                for (int i = Notifications.Count - 1; i >= 0; i--)
                {
                    var notification = Notifications[i];
                    if (cutoffTime - notification.Timestamp > notification.Duration)
                    {
                        Notifications.RemoveAt(i);
                    }
                }
            });
        }
    }

    public class NotificationItem
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public NotificationType Type { get; set; }
        public DateTime Timestamp { get; set; }
        public TimeSpan Duration { get; set; }
        
        public string TypeIcon => Type switch
        {
            NotificationType.Info => "Information",
            NotificationType.Warning => "Warning",
            NotificationType.Error => "Error",
            NotificationType.Success => "CheckCircle",
            _ => "Information"
        };

        public string TypeColor => Type switch
        {
            NotificationType.Info => "#2196F3",
            NotificationType.Warning => "#FF9800",
            NotificationType.Error => "#F44336",
            NotificationType.Success => "#4CAF50",
            _ => "#2196F3"
        };

        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.Now - Timestamp;
                if (timeSpan.TotalMinutes < 1)
                    return "الآن";
                if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes} دقيقة";
                if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours} ساعة";
                return $"{(int)timeSpan.TotalDays} يوم";
            }
        }
    }
}
