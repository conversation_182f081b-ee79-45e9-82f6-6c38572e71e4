<UserControl x:Class="NetworkManagement.Views.InventoryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel>
            <!-- Page Header -->
            <TextBlock Text="إدارة المخزون" Style="{StaticResource PageHeaderStyle}"/>

            <!-- Statistics Cards -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Items Card -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="Package" 
                                                   Width="24" Height="24" 
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إجمالي العناصر" 
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalItems}" 
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="{Binding TotalQuantity, StringFormat='إجمالي الكمية: {0}'}" 
                                  FontSize="12" 
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Low Stock Alert Card -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="AlertCircle" 
                                                   Width="24" Height="24" 
                                                   Foreground="#F44336"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="مخزون منخفض" 
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding LowStockCount}" 
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="#F44336"/>
                        <TextBlock Text="عنصر يحتاج إعادة تموين" 
                                  FontSize="12" 
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Value Card -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="24" Height="24" 
                                                   Foreground="#4CAF50"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="القيمة الإجمالية" 
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalValue, StringFormat='{}{0:C}'}" 
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="#4CAF50"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Quick Actions Card -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="إجراءات سريعة" 
                                  FontSize="14" FontWeight="Medium"
                                  Margin="0,0,0,15"/>
                        
                        <Button Content="إضافة عنصر"
                               Command="{Binding AddItemCommand}"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Margin="0,0,0,5" HorizontalAlignment="Stretch"
                               Visibility="{Binding CanAddItems, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        
                        <Button Content="تقرير المخزون" 
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               HorizontalAlignment="Stretch"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Low Stock Alert Section -->
            <materialDesign:Card Margin="0,0,0,10" Padding="15"
                               Visibility="{Binding LowStockCount, Converter={StaticResource ZeroToVisibilityConverter}, ConverterParameter=Inverse}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="AlertCircle" 
                                               Width="20" Height="20" 
                                               Foreground="#F44336"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="تنبيه: عناصر تحتاج إعادة تموين" 
                                  FontSize="16" FontWeight="Medium"
                                  Foreground="#F44336"
                                  VerticalAlignment="Center" Margin="10,0,0,0"/>
                    </StackPanel>
                    
                    <ItemsControl ItemsSource="{Binding LowStockItems}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#FFEBEE" CornerRadius="4" 
                                       Padding="10,5" Margin="0,0,10,5">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding Quantity, StringFormat=' ({0})'}" 
                                                  Foreground="#F44336" Margin="5,0,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </materialDesign:Card>

            <!-- Filters -->
            <materialDesign:Card Margin="0,0,0,10" Padding="15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Search -->
                    <TextBox Grid.Column="0" 
                            materialDesign:HintAssist.Hint="البحث في المخزون..."
                            materialDesign:HintAssist.IsFloating="False"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Margin="0,0,15,0">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchInventoryCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- Category Filter -->
                    <ComboBox Grid.Column="1" 
                             materialDesign:HintAssist.Hint="فلترة حسب الفئة"
                             ItemsSource="{Binding Categories}"
                             SelectedItem="{Binding SelectedCategory}"
                             MinWidth="150" Margin="0,0,15,0"/>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button Command="{Binding SearchInventoryCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="0,0,10,0">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="بحث"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding LoadInventoryCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="تحديث"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Inventory List -->
            <materialDesign:Card>
                <Grid>
                    <!-- Loading Indicator -->
                    <ProgressBar IsIndeterminate="True" 
                                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                VerticalAlignment="Top" Height="4"/>

                    <!-- Data Grid -->
                    <DataGrid ItemsSource="{Binding InventoryItems}"
                             SelectedItem="{Binding SelectedItem}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             Margin="10">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم العنصر" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="100"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                            <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinimumStock}" Width="100"/>
                            <DataGridTextColumn Header="الحد الأعلى" Binding="{Binding MaximumStock}" Width="100"/>
                            <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice, StringFormat=C}" Width="100"/>
                            <DataGridTextColumn Header="الموقع" Binding="{Binding Location}" Width="120"/>
                            <DataGridTextColumn Header="المورد" Binding="{Binding Supplier}" Width="120"/>
                            
                            <!-- Stock Status Column -->
                            <DataGridTemplateColumn Header="حالة المخزون" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Quantity}" Value="0">
                                                            <Setter Property="Background" Value="#F44336"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                    <Setter Property="Background" Value="#4CAF50"/>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="جيد" Foreground="White" FontSize="12" FontWeight="Medium"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <!-- Actions Column -->
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="تحرير"
                                                   Command="{Binding DataContext.EditItemCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   Margin="0,0,5,0"
                                                   Visibility="{Binding Converter={StaticResource CanEditDataConverter}}"/>
                                            <Button Content="حذف"
                                                   Command="{Binding DataContext.DeleteItemCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Foreground="Red"
                                                   Visibility="{Binding Converter={StaticResource CanDeleteDataConverter}}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Empty State -->
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center"
                               Visibility="{Binding InventoryItems.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="Package" Width="64" Height="64" 
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="لا توجد عناصر في المخزون" 
                                  FontSize="16" 
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="انقر على 'إضافة عنصر' لإضافة العنصر الأول" 
                                  FontSize="12" 
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</UserControl>
