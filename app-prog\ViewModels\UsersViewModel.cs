using System;
using System.Collections.ObjectModel;
using NetworkManagement.Views;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace NetworkManagement.ViewModels
{
    public partial class UsersViewModel : ObservableObject
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;

        [ObservableProperty]
        private ObservableCollection<User> users = new();

        [ObservableProperty]
        private User? selectedUser;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private string selectedRole = "الكل";

        [ObservableProperty]
        private bool isLoading = false;

        // خصائص الصلاحيات
        public bool CanAddUsers => _authService.CanManageUsersInNetwork();
        public bool CanEditUsers => _authService.CanManageUsers;
        public bool CanDeleteUsers => _authService.CanManageUsers;
        public bool CanManageUsers => _authService.CanManageUsers;

        // Threading control
        private readonly System.Threading.SemaphoreSlim _loadSemaphore = new(1, 1);
        private System.Threading.CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new();

        public string[] Roles { get; } = { "الكل", "admin", "manager", "technician" };

        public UsersViewModel(IServiceProvider serviceProvider, IAuthService authService)
        {
            _serviceProvider = serviceProvider;
            _authService = authService;

            // الاشتراك في تغيير المستخدم لتحديث الصلاحيات
            _authService.UserChanged += OnUserChanged;
        }

        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                await LoadUsersAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing users: {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء تحميل المستخدمين:\n{ex.Message}",
                    "خطأ في التحميل",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadUsersAsync()
        {
            await LoadUsersInternalAsync(async (userService) => await userService.GetAllAsync(null));
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SearchUsersAsync()
        {
            await LoadUsersInternalAsync(async (userService) => await userService.SearchAsync(SearchText, null));
        }

        private async System.Threading.Tasks.Task LoadUsersInternalAsync(Func<IUserService, System.Threading.Tasks.Task<System.Collections.Generic.IEnumerable<User>>> dataLoader)
        {
            // منع التداخل في العمليات
            if (!await _loadSemaphore.WaitAsync(100))
            {
                System.Diagnostics.Debug.WriteLine("LoadUsersInternalAsync: عملية أخرى قيد التنفيذ، تم التجاهل");
                return;
            }

            try
            {
                // إلغاء أي عملية سابقة
                lock (_loadLock)
                {
                    _loadCancellationTokenSource?.Cancel();
                    _loadCancellationTokenSource = new System.Threading.CancellationTokenSource();
                }

                IsLoading = true;
                var cancellationToken = _loadCancellationTokenSource.Token;

                // إنشاء scope جديد للحصول على UserService
                using var scope = _serviceProvider.CreateScope();
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

                var userList = await dataLoader(userService);

                // التحقق من الإلغاء
                cancellationToken.ThrowIfCancellationRequested();

                // فلترة المستخدمين حسب الصلاحيات
                var filteredUsers = userList.Where(user => _authService.CanViewData(user.NetworkId));

                // Apply role filter
                if (SelectedRole != "الكل")
                {
                    filteredUsers = filteredUsers.Where(u => u.Role.Equals(SelectedRole, StringComparison.OrdinalIgnoreCase));
                }

                // التحقق من الإلغاء مرة أخرى
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث UI على الـ UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Users.Clear();
                    foreach (var user in filteredUsers)
                    {
                        Users.Add(user);
                    }
                }, System.Windows.Threading.DispatcherPriority.Normal, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("LoadUsersInternalAsync: تم إلغاء العملية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading users: {ex.Message}");

                // عرض رسالة الخطأ على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    System.Windows.MessageBox.Show(
                        $"خطأ في تحميل المستخدمين: {ex.Message}",
                        "خطأ",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                });
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();
            }
        }

        [RelayCommand]
        private void AddUser()
        {
            // التحقق من صلاحية إضافة المستخدمين
            if (!CanAddUsers)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "مستخدمين جدد");
                return;
            }

            var dialogViewModel = App.GetService<UserDialogViewModel>();
            var dialog = new UserDialog(dialogViewModel);

            dialogViewModel.UserSaved += async (s, user) =>
            {
                await LoadUsersAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private void EditUser(User? user = null)
        {
            var userToEdit = user ?? SelectedUser;
            if (userToEdit == null) return;

            // التحقق من صلاحية تعديل المستخدم المحدد
            if (!_authService.CanEditUser(userToEdit))
            {
                PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا المستخدم");
                return;
            }

            var dialogViewModel = App.GetService<UserDialogViewModel>();
            dialogViewModel.SetEditUser(userToEdit);
            var dialog = new UserDialog(dialogViewModel);

            dialogViewModel.UserSaved += async (s, savedUser) =>
            {
                await LoadUsersAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteUserAsync(User? user = null)
        {
            var userToDelete = user ?? SelectedUser;
            if (userToDelete == null) return;

            // التحقق من صلاحية حذف المستخدم المحدد
            if (!_authService.CanDeleteUser(userToDelete))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا المستخدم");
                return;
            }

            var userInfo = $"{userToDelete.Name} ({userToDelete.Role})";
            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف المستخدم:\n{userInfo}؟\n\nلا يمكن التراجع عن هذا الإجراء.",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

                    await userService.DeleteAsync(userToDelete.Id);
                    await LoadUsersAsync();

                    System.Windows.MessageBox.Show(
                        $"تم حذف المستخدم '{userInfo}' بنجاح.",
                        "تم الحذف",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show(
                        $"حدث خطأ أثناء حذف المستخدم:\n{ex.Message}",
                        "خطأ في الحذف",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
            }
        }

        partial void OnSelectedRoleChanged(string value)
        {
            _ = DelayedSearchAsync();
        }

        private async System.Threading.Tasks.Task DelayedSearchAsync()
        {
            try
            {
                await System.Threading.Tasks.Task.Delay(300);
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    await SearchUsersAsync();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in delayed search: {ex.Message}");
            }
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddUsers));
                    OnPropertyChanged(nameof(CanEditUsers));
                    OnPropertyChanged(nameof(CanDeleteUsers));
                    OnPropertyChanged(nameof(CanManageUsers));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadUsersAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // إلغاء الاشتراك في تغيير المستخدم
            _authService.UserChanged -= OnUserChanged;

            // إلغاء أي عمليات تحميل جارية
            lock (_loadLock)
            {
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource?.Dispose();
            }

            // تنظيف SemaphoreSlim
            _loadSemaphore?.Dispose();
        }
    }
}
