using System;
using System.ComponentModel.DataAnnotations;

namespace NetworkManagement.Models
{
    public class Task
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(50)]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; } = DateTime.Now;

        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = "pending"; // pending, in-progress, completed, cancelled

        [MaxLength(1000)]
        public string? Notes { get; set; }

        [MaxLength(20)]
        public string? Priority { get; set; } = "medium"; // low, medium, high, urgent

        public DateTime? DueDate { get; set; }

        public DateTime? CompletedAt { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual User? User { get; set; }

        // Display properties
        public string StatusDisplay => Status switch
        {
            "pending" => "معلق",
            "in-progress" => "قيد التنفيذ",
            "completed" => "مكتمل",
            "cancelled" => "ملغي",
            _ => "غير محدد"
        };

        public string PriorityDisplay => Priority switch
        {
            "low" => "منخفضة",
            "medium" => "متوسطة",
            "high" => "عالية",
            "urgent" => "عاجلة",
            _ => "متوسطة"
        };

        public string StatusColor => Status switch
        {
            "pending" => "#FF9800",
            "in-progress" => "#2196F3",
            "completed" => "#4CAF50",
            "cancelled" => "#F44336",
            _ => "#9E9E9E"
        };

        public string PriorityColor => Priority switch
        {
            "low" => "#4CAF50",
            "medium" => "#2196F3",
            "high" => "#FF9800",
            "urgent" => "#F44336",
            _ => "#2196F3"
        };

        public string DueDateDisplay => DueDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string CompletedAtDisplay => CompletedAt?.ToString("dd/MM/yyyy") ?? "غير مكتمل";
        public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy");

        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.Now && Status != "completed";
        public bool IsCompleted => Status == "completed";
        public bool CanComplete => Status != "completed" && Status != "cancelled";
    }
}
