using System;
using System.Threading.Tasks;
using System.Windows;
using MaterialDesignThemes.Wpf;

namespace NetworkManagement.Services
{
    public class ThemeService : IThemeService
    {
        private readonly ISettingsService _settingsService;
        private string _currentTheme = "Light";

        public ThemeService(ISettingsService settingsService)
        {
            _settingsService = settingsService;
        }

        public async Task ApplyThemeAsync(string themeName)
        {
            try
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    var paletteHelper = new PaletteHelper();
                    var theme = paletteHelper.GetTheme();

                    switch (themeName.ToLower())
                    {
                        case "dark":
                            theme.SetBaseTheme(Theme.Dark);
                            _currentTheme = "Dark";
                            break;
                        case "light":
                        default:
                            theme.SetBaseTheme(Theme.Light);
                            _currentTheme = "Light";
                            break;
                    }

                    paletteHelper.SetTheme(theme);
                });

                // حفظ المظهر في الإعدادات
                try
                {
                    var settings = await _settingsService.GetSettingsAsync();
                    var updatedSettings = new
                    {
                        RememberLogin = settings.RememberLogin,
                        DefaultNetwork = settings.DefaultNetwork ?? "",
                        PingTimeout = settings.PingTimeout,
                        ShowNotifications = settings.ShowNotifications,
                        Theme = _currentTheme,
                        Language = settings.Language ?? "العربية",
                        BackupLocation = settings.BackupLocation ?? "",
                        AutoBackupEnabled = settings.AutoBackupEnabled,
                        AutoBackupDays = settings.AutoBackupDays
                    };

                    await _settingsService.SaveSettingsAsync(updatedSettings);
                }
                catch (Exception settingsEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Error saving theme settings: {settingsEx.Message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying theme: {ex.Message}");
                throw;
            }
        }

        public string GetCurrentTheme()
        {
            return _currentTheme;
        }

        public string[] GetAvailableThemes()
        {
            return new[] { "Light", "Dark" };
        }

        public async Task ApplyDefaultThemeAsync()
        {
            await ApplyThemeAsync("Light");
        }

        public async Task InitializeThemeAsync()
        {
            try
            {
                var settings = await _settingsService.GetSettingsAsync();
                var savedTheme = settings.Theme ?? "Light";
                await ApplyThemeAsync(savedTheme);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing theme: {ex.Message}");
                await ApplyDefaultThemeAsync();
            }
        }
    }
}
