using System;
using System.ComponentModel.DataAnnotations;

namespace NetworkManagement.Models
{
    public class Purchase
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(100)]
        public string ItemType { get; set; } = string.Empty;

        [Required]
        public decimal Price { get; set; }

        [Required]
        public DateTime Date { get; set; } = DateTime.Now;

        public int? Quantity { get; set; } = 1;

        [MaxLength(20)]
        public string? Unit { get; set; }

        [MaxLength(200)]
        public string? Supplier { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(50)]
        public string? InvoiceNumber { get; set; }

        [MaxLength(20)]
        public string? Category { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
