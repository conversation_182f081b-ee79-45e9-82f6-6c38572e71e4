using System;
using System.Collections.ObjectModel;
using NetworkManagement.Views;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Helpers;

namespace NetworkManagement.ViewModels
{
    public partial class InventoryViewModel : ObservableObject
    {
        private readonly IAuthService _authService;
        private readonly IServiceProvider _serviceProvider;

        [ObservableProperty]
        private ObservableCollection<Inventory> inventoryItems = new();

        [ObservableProperty]
        private ObservableCollection<Inventory> lowStockItems = new();

        [ObservableProperty]
        private Inventory? selectedItem;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private string selectedCategory = "الكل";

        [ObservableProperty]
        private bool isLoading = false;

        // Statistics
        [ObservableProperty]
        private int totalItems;

        [ObservableProperty]
        private int lowStockCount;

        [ObservableProperty]
        private decimal totalValue;

        [ObservableProperty]
        private int totalQuantity;

        public string[] Categories { get; } = { "الكل", "أجهزة", "كابلات", "أدوات", "قطع غيار", "مواد استهلاكية", "أخرى" };

        // Permission properties
        public bool CanAddItems => _authService.CanAddData();
        public bool CanEditItems => _authService.CanEditData();
        public bool CanDeleteItems => _authService.CanDeleteData();
        public bool CanManageInventory => _authService.CanManageInventory;

        public InventoryViewModel(IAuthService authService, IServiceProvider serviceProvider)
        {
            _authService = authService;
            _serviceProvider = serviceProvider;

            // Subscribe to user changes to update permissions
            _authService.UserChanged += OnUserChanged;

            _ = LoadInventoryAsync(); // Fire and forget - safe for initial load
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadInventoryAsync()
        {
            try
            {
                IsLoading = true;

                // إنشاء scope جديد للحصول على InventoryService
                using var scope = _serviceProvider.CreateScope();
                var inventoryService = scope.ServiceProvider.GetRequiredService<IInventoryService>();

                // الحصول على فلتر الشبكة
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var inventoryList = await inventoryService.GetAllAsync(networkFilter);

                // تطبيق فلترة الصلاحيات باستخدام PermissionHelper
                inventoryList = PermissionHelper.ApplyPermissionFilter(inventoryList, _authService, item => item.NetworkId);

                // Apply filters
                if (!string.IsNullOrEmpty(SearchText))
                {
                    inventoryList = inventoryList.Where(i =>
                        i.Name.Contains(SearchText) ||
                        (i.Description?.Contains(SearchText) ?? false) ||
                        i.Category.Contains(SearchText));
                }

                if (SelectedCategory != "الكل")
                {
                    inventoryList = inventoryList.Where(i => i.Category == SelectedCategory);
                }

                InventoryItems.Clear();
                foreach (var item in inventoryList.OrderBy(i => i.Name))
                {
                    InventoryItems.Add(item);
                }

                // Load low stock items with permissions filter
                var lowStock = await inventoryService.GetLowStockItemsAsync(networkFilter);
                var filteredLowStock = PermissionHelper.ApplyPermissionFilter(lowStock, _authService, item => item.NetworkId);
                LowStockItems.Clear();
                foreach (var item in filteredLowStock)
                {
                    LowStockItems.Add(item);
                }

                await LoadStatisticsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading inventory: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SearchInventoryAsync()
        {
            await LoadInventoryAsync();
        }

        [RelayCommand]
        private void AddItem()
        {
            // التحقق من صلاحية إضافة عناصر المخزون
            if (!CanAddItems)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "عناصر مخزون جديدة");
                return;
            }

            var dialogViewModel = App.GetService<InventoryDialogViewModel>();
            var dialog = new InventoryDialog(dialogViewModel);

            dialogViewModel.InventorySaved += async (s, inventory) =>
            {
                await LoadInventoryAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private void EditItem()
        {
            if (SelectedItem != null)
            {
                // التحقق من صلاحية تعديل عنصر المخزون
                if (!PermissionHelper.CanEditItem(_authService, SelectedItem.NetworkId))
                {
                    PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا العنصر");
                    return;
                }

                var dialogViewModel = App.GetService<InventoryDialogViewModel>();
                dialogViewModel.SetEditInventory(SelectedItem);
                var dialog = new InventoryDialog(dialogViewModel);

                dialogViewModel.InventorySaved += async (s, inventory) =>
                {
                    await LoadInventoryAsync();
                };

                dialog.ShowDialog();
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteItemAsync()
        {
            if (SelectedItem != null)
            {
                // التحقق من صلاحية حذف عنصر المخزون
                if (!PermissionHelper.CanDeleteItem(_authService, SelectedItem.NetworkId))
                {
                    PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا العنصر");
                    return;
                }

                var itemInfo = $"{SelectedItem.Name} - الكمية: {SelectedItem.Quantity}";
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العنصر:\n{itemInfo}؟\n\nلا يمكن التراجع عن هذا الإجراء.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // إنشاء scope جديد للحصول على InventoryService
                        using var scope = _serviceProvider.CreateScope();
                        var inventoryService = scope.ServiceProvider.GetRequiredService<IInventoryService>();

                        await inventoryService.DeleteAsync(SelectedItem.Id);
                        await LoadInventoryAsync();

                        MessageBox.Show(
                            $"تم حذف العنصر '{itemInfo}' بنجاح.",
                            "تم الحذف",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(
                            $"حدث خطأ أثناء حذف العنصر:\n{ex.Message}",
                            "خطأ في الحذف",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
        }

        private System.Threading.Tasks.Task LoadStatisticsAsync()
        {
            try
            {
                // Calculate statistics from current filtered items
                TotalItems = InventoryItems.Count;
                LowStockCount = LowStockItems.Count;
                TotalQuantity = InventoryItems.Sum(i => i.Quantity);

                // Calculate total value (quantity * unit price)
                TotalValue = InventoryItems
                    .Where(i => i.UnitPrice.HasValue)
                    .Sum(i => i.Quantity * (i.UnitPrice ?? 0));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
            }

            return System.Threading.Tasks.Task.CompletedTask;
        }

        partial void OnSelectedCategoryChanged(string value)
        {
            _ = LoadInventoryAsync();
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddItems));
                    OnPropertyChanged(nameof(CanEditItems));
                    OnPropertyChanged(nameof(CanDeleteItems));
                    OnPropertyChanged(nameof(CanManageInventory));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadInventoryAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        // Helper method to determine stock status color
        public string GetStockStatusColor(Inventory item)
        {
            if (item.MinimumStock.HasValue && item.Quantity <= item.MinimumStock.Value)
            {
                return "#F44336"; // Red for low stock
            }
            else if (item.MinimumStock.HasValue && item.Quantity <= item.MinimumStock.Value * 1.5)
            {
                return "#FF9800"; // Orange for warning
            }
            else
            {
                return "#4CAF50"; // Green for good stock
            }
        }

        // Helper method to get stock status text
        public string GetStockStatusText(Inventory item)
        {
            if (item.MinimumStock.HasValue && item.Quantity <= item.MinimumStock.Value)
            {
                return "منخفض";
            }
            else if (item.MinimumStock.HasValue && item.Quantity <= item.MinimumStock.Value * 1.5)
            {
                return "تحذير";
            }
            else
            {
                return "جيد";
            }
        }
    }
}
