using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;
using Task = System.Threading.Tasks.Task;

namespace NetworkManagement.ViewModels
{
    public partial class NetworkManagementViewModel : ObservableObject
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;

        [ObservableProperty]
        private ObservableCollection<Network> networks = new();

        [ObservableProperty]
        private Network? selectedNetwork;

        [ObservableProperty]
        private bool isLoading;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private int selectedNetworkUsersCount = 0;

        [ObservableProperty]
        private int selectedNetworkDevicesCount = 0;

        [ObservableProperty]
        private int selectedNetworkSitesCount = 0;

        // Threading control
        private readonly SemaphoreSlim _loadSemaphore = new(1, 1);
        private CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new();

        // خصائص الصلاحيات
        public bool CanManageNetworks => _authService.CanManageNetworks;
        public bool CanAddNetworks => _authService.IsSuperAdmin;
        public bool CanEditNetworks => _authService.IsSuperAdmin;
        public bool CanDeleteNetworks => _authService.IsSuperAdmin;
        public bool CanViewAllNetworks => _authService.CanViewAllNetworks;

        public NetworkManagementViewModel(IServiceProvider serviceProvider, IAuthService authService)
        {
            _serviceProvider = serviceProvider;
            _authService = authService;

            // الاشتراك في تغييرات المستخدم
            _authService.UserChanged += OnUserChanged;
        }

        public async Task InitializeAsync()
        {
            try
            {
                await LoadNetworksAsync();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تهيئة صفحة إدارة الشبكات: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"NetworkManagementViewModel.InitializeAsync error: {ex}");
            }
        }

        [RelayCommand]
        private async Task LoadNetworksAsync()
        {
            // إلغاء العملية السابقة إن وجدت
            lock (_loadLock)
            {
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource = new CancellationTokenSource();
            }

            var cancellationToken = _loadCancellationTokenSource.Token;

            if (!await _loadSemaphore.WaitAsync(100, cancellationToken))
                return;

            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                // إنشاء scope جديد للحصول على NetworkService
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();

                // تحميل الشبكات مع تطبيق فلترة الصلاحيات
                var networkList = await NetworkHelper.LoadFilteredNetworksAsync(
                    _authService,
                    networkService,
                    cancellationToken);

                // التحقق من الإلغاء
                cancellationToken.ThrowIfCancellationRequested();

                // تطبيق فلتر البحث إذا وجد
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    networkList = networkList.Where(n =>
                        n.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        (n.Description?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
                }

                // تحديث UI على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Networks.Clear();
                    foreach (var network in networkList)
                    {
                        Networks.Add(network);
                    }
                });
            }
            catch (OperationCanceledException)
            {
                // تم إلغاء العملية - لا نفعل شيء
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل الشبكات: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"LoadNetworksAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();
            }
        }

        [RelayCommand]
        private async Task AddNetworkAsync()
        {
            try
            {
                // التحقق من صلاحية إضافة الشبكات
                if (!CanAddNetworks)
                {
                    NetworkHelper.ShowNetworkPermissionDeniedMessage("إضافة");
                    return;
                }

                IsLoading = true;
                ErrorMessage = string.Empty;

                // إنشاء scope جديد للحصول على NetworkService
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();

                var newNetwork = NetworkHelper.CreateNewNetwork();

                await networkService.CreateAsync(newNetwork);
                await LoadNetworksAsync();

                SelectedNetwork = Networks.FirstOrDefault(n => n.Id == newNetwork.Id);

                System.Windows.MessageBox.Show(
                    "تم إضافة الشبكة بنجاح!",
                    "نجح الإضافة",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                var detailedError = ex.InnerException?.Message ?? ex.Message;
                ErrorMessage = $"خطأ في إضافة الشبكة: {detailedError}";

                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء إضافة الشبكة:\n\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}",
                    "خطأ في الإضافة",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);

                System.Diagnostics.Debug.WriteLine($"Network creation error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task SaveNetworkAsync()
        {
            try
            {
                if (SelectedNetwork == null) return;

                // التحقق من صلاحية تعديل الشبكات
                if (!CanEditNetworks)
                {
                    NetworkHelper.ShowNetworkPermissionDeniedMessage("تعديل");
                    return;
                }

                // التحقق من صلاحية تعديل هذه الشبكة تحديداً
                if (!NetworkHelper.CanManageNetwork(_authService, SelectedNetwork.Id))
                {
                    NetworkHelper.ShowNetworkPermissionDeniedMessage("تعديل");
                    return;
                }

                IsLoading = true;
                ErrorMessage = string.Empty;

                // إنشاء scope جديد للحصول على NetworkService
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();

                await networkService.UpdateAsync(SelectedNetwork);
                await LoadNetworksAsync();

                System.Windows.MessageBox.Show(
                    "تم حفظ تغييرات الشبكة بنجاح!",
                    "تم الحفظ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ الشبكة: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"SaveNetworkAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task DeleteNetworkAsync()
        {
            try
            {
                if (SelectedNetwork == null) return;

                // التحقق من صلاحية حذف الشبكات
                if (!CanDeleteNetworks)
                {
                    NetworkHelper.ShowNetworkPermissionDeniedMessage("حذف");
                    return;
                }

                // التحقق من صلاحية حذف هذه الشبكة تحديداً
                if (!NetworkHelper.CanManageNetwork(_authService, SelectedNetwork.Id))
                {
                    NetworkHelper.ShowNetworkPermissionDeniedMessage("حذف");
                    return;
                }

                // تأكيد الحذف من المستخدم
                var result = System.Windows.MessageBox.Show(
                    $"هل أنت متأكد من حذف الشبكة '{SelectedNetwork.Name}'؟\n\nسيتم حذف جميع البيانات المرتبطة بها.",
                    "تأكيد الحذف",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result != System.Windows.MessageBoxResult.Yes)
                    return;

                IsLoading = true;
                ErrorMessage = string.Empty;

                // إنشاء scope جديد للحصول على NetworkService
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();

                // تحميل الشبكة مع البيانات المرتبطة للتحقق
                var networkWithData = await networkService.GetByIdAsync(SelectedNetwork.Id);
                if (networkWithData != null)
                {
                    // التحقق من وجود بيانات مرتبطة
                    if (NetworkHelper.HasRelatedData(networkWithData))
                    {
                        ErrorMessage = "لا يمكن حذف الشبكة لوجود بيانات مرتبطة بها (مستخدمين، أجهزة، أو مواقع)";
                        return;
                    }
                }

                await networkService.DeleteAsync(SelectedNetwork.Id);
                await LoadNetworksAsync();

                SelectedNetwork = null;

                System.Windows.MessageBox.Show(
                    "تم حذف الشبكة بنجاح!",
                    "تم الحذف",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حذف الشبكة: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"DeleteNetworkAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ToggleNetworkStatusAsync()
        {
            try
            {
                if (SelectedNetwork == null) return;

                // التحقق من صلاحية تعديل الشبكات
                if (!CanEditNetworks)
                {
                    NetworkHelper.ShowNetworkPermissionDeniedMessage("تعديل حالة");
                    return;
                }

                // التحقق من صلاحية تعديل هذه الشبكة تحديداً
                if (!NetworkHelper.CanManageNetwork(_authService, SelectedNetwork.Id))
                {
                    NetworkHelper.ShowNetworkPermissionDeniedMessage("تعديل حالة");
                    return;
                }

                var oldStatus = SelectedNetwork.IsActive;
                SelectedNetwork.IsActive = !SelectedNetwork.IsActive;

                IsLoading = true;
                ErrorMessage = string.Empty;

                // إنشاء scope جديد للحصول على NetworkService
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();

                await networkService.UpdateAsync(SelectedNetwork);
                await LoadNetworksAsync();

                var statusText = SelectedNetwork.IsActive ? "تفعيل" : "إيقاف";
                System.Windows.MessageBox.Show(
                    $"تم {statusText} الشبكة بنجاح!",
                    "تم التحديث",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تغيير حالة الشبكة: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"ToggleNetworkStatusAsync error: {ex}");

                // إعادة الحالة السابقة في حالة الخطأ
                if (SelectedNetwork != null)
                {
                    SelectedNetwork.IsActive = !SelectedNetwork.IsActive;
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task SearchNetworksAsync()
        {
            // استخدام LoadNetworksAsync بدلاً من تكرار الكود
            await LoadNetworksAsync();
        }

        partial void OnSearchTextChanged(string value)
        {
            _ = SearchNetworksAsync();
        }

        partial void OnSelectedNetworkChanged(Network? value)
        {
            UpdateSelectedNetworkStatistics();
        }

        private void UpdateSelectedNetworkStatistics()
        {
            if (SelectedNetwork != null)
            {
                SelectedNetworkUsersCount = SelectedNetwork.Users?.Count ?? 0;
                SelectedNetworkDevicesCount = SelectedNetwork.Devices?.Count ?? 0;
                SelectedNetworkSitesCount = SelectedNetwork.Sites?.Count ?? 0;
            }
            else
            {
                SelectedNetworkUsersCount = 0;
                SelectedNetworkDevicesCount = 0;
                SelectedNetworkSitesCount = 0;
            }
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanManageNetworks));
                    OnPropertyChanged(nameof(CanAddNetworks));
                    OnPropertyChanged(nameof(CanEditNetworks));
                    OnPropertyChanged(nameof(CanDeleteNetworks));
                    OnPropertyChanged(nameof(CanViewAllNetworks));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadNetworksAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _authService.UserChanged -= OnUserChanged;
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource?.Dispose();
            _loadSemaphore?.Dispose();
        }
    }
}
