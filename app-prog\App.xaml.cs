using System;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NetworkManagement.Data;
using NetworkManagement.Services;
using NetworkManagement.ViewModels;
using NetworkManagement.Views;
using Microsoft.EntityFrameworkCore;

namespace NetworkManagement
{
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Database
                    services.AddDbContext<NetworkDbContext>(options =>
                    {
                        var settings = new SettingsService().LoadSettings();
                        var connectionString = $"Server={settings.MySQLServer};Database={settings.MySQLDatabase};User={settings.MySQLUser};Password={settings.MySQLPassword}";
                        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
                    }, ServiceLifetime.Scoped);

                    // Services
                    services.AddSingleton<IPingService, PingService>();
                    services.AddSingleton<ISettingsService, SettingsService>();
                    services.AddSingleton<IDatabaseService, DatabaseService>();
                    services.AddSingleton<IThemeService, ThemeService>();
                    services.AddSingleton<ILocalizationService, LocalizationService>();
                    services.AddSingleton<INotificationService, NotificationService>();
                    services.AddSingleton<IPasswordHashService, PasswordHashService>();

                    // Auth service as Singleton (يحتاج للبقاء عبر التطبيق)
                    services.AddSingleton<IAuthService, AuthService>();

                    // Data services as Scoped (لتجنب مشاكل DbContext Threading)
                    services.AddScoped<IDeviceService, DeviceService>();
                    services.AddScoped<ISiteService, SiteService>();
                    services.AddScoped<IUserService, UserService>();
                    services.AddScoped<ITaskService, TaskService>();
                    services.AddScoped<IPurchaseService, PurchaseService>();
                    services.AddScoped<IInventoryService, InventoryService>();
                    services.AddScoped<IReportExportService, ReportExportService>();
            services.AddScoped<IDatabaseRepairService, DatabaseRepairService>();
                    services.AddScoped<IProfessionalReportService, ProfessionalReportService>();
                    services.AddScoped<INetworkService, NetworkService>();



                    // ViewModels
                    services.AddTransient<LoginViewModel>();
                    services.AddTransient<DatabaseSetupViewModel>();
                    services.AddTransient<MainViewModel>();
                    services.AddTransient<DashboardViewModel>();
                    services.AddTransient<DevicesViewModel>();
                    services.AddTransient<SitesViewModel>();
                    services.AddTransient<UsersViewModel>();
                    services.AddTransient<TasksViewModel>();
                    services.AddTransient<PurchasesViewModel>();
                    services.AddTransient<InventoryViewModel>();
                    services.AddTransient<ReportsViewModel>();
                    services.AddTransient<SettingsViewModel>();
                    services.AddTransient<NetworkManagementViewModel>();

                    // Dialog ViewModels
                    services.AddTransient<DeviceDialogViewModel>();
                    services.AddTransient<SiteDialogViewModel>();
                    services.AddTransient<UserDialogViewModel>();
                    services.AddTransient<PurchaseDialogViewModel>();
                    services.AddTransient<InventoryDialogViewModel>();
                    services.AddTransient<TaskDialogViewModel>();
                })
                .Build();

            // Check database connection first
            try
            {
                var settingsService = _host.Services.GetRequiredService<ISettingsService>();
                var canConnect = await settingsService.TestDatabaseConnectionAsync();

                if (!canConnect)
                {
                    // Show database setup window
                    await ShowDatabaseSetupWindowAsync();
                }
                else
                {
                    // Initialize database and show login
                    await InitializeDatabaseAndShowLoginAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
                return;
            }

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }

        public static T GetService<T>() where T : class
        {
            var app = (App)Current;
            return app._host!.Services.GetRequiredService<T>();
        }

        public static IServiceScope CreateScope()
        {
            var app = (App)Current;
            return app._host!.Services.CreateScope();
        }

        private async Task ShowDatabaseSetupWindowAsync()
        {
            var databaseSetupWindow = new DatabaseSetupWindow();
            var viewModel = _host?.Services.GetRequiredService<DatabaseSetupViewModel>();
            if (viewModel != null)
            {
                databaseSetupWindow.DataContext = viewModel;

                // Handle events
                viewModel.DatabaseSetupCompleted += async () =>
                {
                    databaseSetupWindow.Close();
                    await InitializeDatabaseAndShowLoginAsync();
                };

                viewModel.ExitRequested += () =>
                {
                    databaseSetupWindow.Close();
                    Shutdown();
                };
            }

            databaseSetupWindow.ShowDialog();
            await Task.CompletedTask; // To satisfy async method requirement
        }

        private async Task InitializeDatabaseAndShowLoginAsync()
        {
            try
            {
                // Initialize database
                var context = _host?.Services.GetRequiredService<NetworkDbContext>();
                var passwordHashService = _host?.Services.GetRequiredService<IPasswordHashService>();
                if (context != null)
                {
                    await DatabaseInitializer.InitializeAsync(context, passwordHashService);
                }

                // Show login window
                var loginWindow = new LoginWindow();
                loginWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }
    }
}
