using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface IUserService
    {
        Task<IEnumerable<User>> GetAllAsync(string? networkFilter = null);
        Task<User?> GetByIdAsync(string id);
        Task<User> CreateAsync(User user);
        Task<User> UpdateAsync(User user);
        Task<bool> DeleteAsync(string id);
        Task<IEnumerable<User>> SearchAsync(string searchTerm, string? networkFilter = null);
    }
}

