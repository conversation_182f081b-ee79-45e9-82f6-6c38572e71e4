using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace NetworkManagement.Views
{
    public partial class DevicesView : UserControl
    {
        private DevicesViewModel? _viewModel;

        public DevicesView()
        {
            InitializeComponent();
            Loaded += DevicesView_Loaded;
        }

        private void DevicesDataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            e.Row.Header = (e.Row.GetIndex() + 1).ToString();
        }

        private async void DevicesView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: بدء تحميل صفحة الأجهزة");

                // الحصول على ViewModel مباشرة من مزود الخدمة الرئيسي
                _viewModel = App.GetService<DevicesViewModel>();
                DataContext = _viewModel;

                System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: تم الحصول على DevicesViewModel");
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: DataContext = {DataContext}");
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: ViewModel = {_viewModel}");

                // تحقق من وجود فلتر شبكة في Tag
                if (Tag is string networkId && !string.IsNullOrEmpty(networkId))
                {
                    System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: تطبيق فلتر الشبكة - {networkId}");
                    _viewModel.SetNetworkFilter(networkId);
                }

                await _viewModel.InitializeAsync();
                System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: تم تحميل الصفحة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: خطأ - {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: تفاصيل الخطأ - {ex}");

                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء تحميل الأجهزة:\n{ex.Message}",
                    "خطأ في تحميل الأجهزة",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
