<UserControl x:Class="NetworkManagement.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel>
            <!-- Page Header -->
            <TextBlock Text="التقارير والإحصائيات" Style="{StaticResource PageHeaderStyle}"/>

            <!-- Report Controls -->
            <materialDesign:Card Margin="0,0,0,20" Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- First Row: Report Type and Actions -->
                    <Grid Grid.Row="0" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="نوع التقرير:"
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>

                        <ComboBox Grid.Column="1"
                                 ItemsSource="{Binding ReportTypes}"
                                 SelectedItem="{Binding SelectedReportType}"
                                 Margin="0,0,20,0"/>

                        <!-- Spacer -->
                        <Grid Grid.Column="2"/>

                        <!-- Action Buttons -->
                        <StackPanel Grid.Column="3" Orientation="Horizontal"
                                   Visibility="{Binding CanViewReports, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button Content="إنشاء التقرير"
                                   Command="{Binding GenerateReportCommand}"
                                   Style="{StaticResource MaterialDesignRaisedButton}"
                                   Margin="0,0,10,0"/>

                            <Button Content="تصدير عادي"
                                   Command="{Binding ExportReportCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Margin="0,0,10,0"
                                   ToolTip="تصدير إلى Excel أو CSV"
                                   Visibility="{Binding CanExportReports, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                            <!-- Professional Report Buttons -->
                            <Button Content="Excel احترافي"
                                   Command="{Binding ExportProfessionalExcelCommand}"
                                   Style="{StaticResource MaterialDesignRaisedButton}"
                                   Background="#4CAF50"
                                   Margin="0,0,5,0"
                                   ToolTip="تصدير تقرير Excel احترافي مع تنسيق وإحصائيات"
                                   Visibility="{Binding CanExportReports, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                            <Button Content="PDF احترافي"
                                   Command="{Binding ExportProfessionalPdfCommand}"
                                   Style="{StaticResource MaterialDesignRaisedButton}"
                                   Background="#F44336"
                                   Margin="0,0,5,0"
                                   ToolTip="تصدير تقرير PDF احترافي"
                                   Visibility="{Binding CanExportReports, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                            <Button Content="طباعة احترافية"
                                   Command="{Binding PrintProfessionalReportCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Margin="0,0,5,0"
                                   ToolTip="طباعة التقرير بتنسيق احترافي"
                                   Visibility="{Binding CanPrintReports, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                            <Button Content="معاينة"
                                   Command="{Binding PreviewReportCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   ToolTip="معاينة التقرير قبل الطباعة"/>
                        </StackPanel>
                    </Grid>

                    <!-- Second Row: Filters -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Date Range -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="من تاريخ:"
                                      VerticalAlignment="Center" Margin="0,0,10,0"/>

                            <DatePicker Grid.Column="1"
                                       SelectedDate="{Binding StartDate}"
                                       Margin="0,0,20,0"/>

                            <TextBlock Grid.Column="2" Text="إلى تاريخ:"
                                      VerticalAlignment="Center" Margin="0,0,10,0"/>

                            <DatePicker Grid.Column="3"
                                       SelectedDate="{Binding EndDate}"/>
                        </Grid>

                        <!-- Network and User Filters -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="الشبكة:"
                                      VerticalAlignment="Center" Margin="0,0,10,0"/>

                            <ComboBox Grid.Column="1"
                                     ItemsSource="{Binding AvailableNetworks}"
                                     SelectedValue="{Binding SelectedNetworkId}"
                                     SelectedValuePath="Id"
                                     DisplayMemberPath="Name"
                                     Margin="0,0,20,0"/>

                            <TextBlock Grid.Column="2" Text="المستخدم:"
                                      VerticalAlignment="Center" Margin="0,0,10,0"/>

                            <ComboBox Grid.Column="3"
                                     ItemsSource="{Binding AvailableUsers}"
                                     SelectedValue="{Binding SelectedUserId}"
                                     SelectedValuePath="Id"
                                     DisplayMemberPath="Name"/>
                        </Grid>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Loading Indicator -->
            <ProgressBar IsIndeterminate="True"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Height="4" Margin="0,0,0,10"/>

            <!-- General Statistics Cards -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Devices Card -->
                <materialDesign:Card Grid.Column="0" Grid.Row="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="Router"
                                                   Width="24" Height="24"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="الأجهزة"
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalDevices}"
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="{Binding ActiveDevices, StringFormat='نشط: {0}'}"
                                  FontSize="12"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Sites Card -->
                <materialDesign:Card Grid.Column="1" Grid.Row="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="MapMarker"
                                                   Width="24" Height="24"
                                                   Foreground="#4CAF50"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="المواقع"
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalSites}"
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="#4CAF50"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Users Card -->
                <materialDesign:Card Grid.Column="2" Grid.Row="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="AccountGroup"
                                                   Width="24" Height="24"
                                                   Foreground="#FF9800"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="المستخدمين"
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalUsers}"
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="#FF9800"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Purchases Card -->
                <materialDesign:Card Grid.Column="3" Grid.Row="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="CurrencyUsd"
                                                   Width="24" Height="24"
                                                   Foreground="#9C27B0"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="المشتريات"
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalPurchases, StringFormat='{}{0:C}'}"
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="#9C27B0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Inventory Card -->
                <materialDesign:Card Grid.Column="0" Grid.Row="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="Package"
                                                   Width="24" Height="24"
                                                   Foreground="#607D8B"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="المخزون"
                                      FontSize="14" FontWeight="Medium"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding TotalInventoryItems}"
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="#607D8B"/>
                        <TextBlock Text="{Binding LowStockItems, StringFormat='منخفض: {0}'}"
                                  FontSize="12"
                                  Foreground="#F44336"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Charts and Detailed Reports -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Network Statistics -->
                <materialDesign:Card Grid.Column="0" Margin="0,0,10,0">
                    <StackPanel Margin="15">
                        <TextBlock Text="إحصائيات الشبكات"
                                  FontSize="16" FontWeight="Medium"
                                  Margin="0,0,0,15"/>

                        <DataGrid ItemsSource="{Binding NetworkStatistics}"
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 IsReadOnly="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 MaxHeight="300">

                            <DataGrid.Columns>
                                <DataGridTextColumn Header="الشبكة" Binding="{Binding NetworkName}" Width="*"/>
                                <DataGridTextColumn Header="الأجهزة" Binding="{Binding DeviceCount}" Width="60"/>
                                <DataGridTextColumn Header="نشط" Binding="{Binding ActiveCount}" Width="50"/>
                                <DataGridTextColumn Header="غير نشط" Binding="{Binding InactiveCount}" Width="70"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Device Status Distribution -->
                <materialDesign:Card Grid.Column="1" Margin="10,0,0,0">
                    <StackPanel Margin="15">
                        <TextBlock Text="توزيع حالة الأجهزة"
                                  FontSize="16" FontWeight="Medium"
                                  Margin="0,0,0,15"/>

                        <DataGrid ItemsSource="{Binding DeviceStatusCounts}"
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 IsReadOnly="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 MaxHeight="300">

                            <DataGrid.Columns>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="*"/>
                                <DataGridTextColumn Header="العدد" Binding="{Binding Count}" Width="60"/>
                                <DataGridTextColumn Header="النسبة %" Binding="{Binding Percentage}" Width="70"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Monthly Purchases Trend -->
            <materialDesign:Card Margin="0,20,0,0">
                <StackPanel Margin="15">
                    <TextBlock Text="اتجاه المشتريات الشهرية (آخر 12 شهر)"
                              FontSize="16" FontWeight="Medium"
                              Margin="0,0,0,15"/>

                    <DataGrid ItemsSource="{Binding MonthlyPurchases}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             MaxHeight="400">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الشهر" Binding="{Binding Month}" Width="100"/>
                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=C}" Width="120"/>
                            <DataGridTextColumn Header="عدد المشتريات" Binding="{Binding Count}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Report Data Display -->
            <materialDesign:Card Margin="0,20,0,0">
                <StackPanel Margin="15">
                    <TextBlock Text="بيانات التقرير"
                              FontSize="16" FontWeight="Medium"
                              Margin="0,0,0,15"/>

                    <!-- Users Report Data -->
                    <DataGrid ItemsSource="{Binding ReportUsers}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             MaxHeight="400"
                             Visibility="{Binding IsUsersReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="120"/>
                            <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="الدور" Binding="{Binding Role}" Width="100"/>
                            <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                            <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Devices Report Data -->
                    <DataGrid ItemsSource="{Binding ReportDevices}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             MaxHeight="400"
                             Visibility="{Binding IsDevicesReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم الجهاز" Binding="{Binding DeviceName}" Width="150"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding DeviceType}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                            <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                            <DataGridTextColumn Header="الموقع" Binding="{Binding Site.Name}" Width="120"/>
                            <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Sites Report Data -->
                    <DataGrid ItemsSource="{Binding ReportSites}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             MaxHeight="400"
                             Visibility="{Binding IsSitesReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم الموقع" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="200"/>
                            <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="100"/>
                            <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                            <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Tasks Report Data -->
                    <DataGrid ItemsSource="{Binding ReportTasks}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             MaxHeight="400"
                             Visibility="{Binding IsTasksReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="100"/>
                            <DataGridTextColumn Header="الأولوية" Binding="{Binding PriorityDisplay}" Width="100"/>
                            <DataGridTextColumn Header="المستخدم" Binding="{Binding User.Name}" Width="120"/>
                            <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                            <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Purchases Report Data -->
                    <DataGrid ItemsSource="{Binding ReportPurchases}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             MaxHeight="400"
                             Visibility="{Binding IsPurchasesReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="نوع الصنف" Binding="{Binding ItemType}" Width="150"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat=C}" Width="100"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy/MM/dd}" Width="100"/>
                            <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Inventory Report Data -->
                    <DataGrid ItemsSource="{Binding ReportInventory}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             MaxHeight="400"
                             Visibility="{Binding IsInventoryReportVisible, Converter={StaticResource BooleanToVisibilityConverter}}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم الصنف" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="120"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                            <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name}" Width="120"/>
                            <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</UserControl>
