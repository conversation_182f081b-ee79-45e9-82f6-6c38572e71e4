using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface ITaskService
    {
        Task<IEnumerable<Models.Task>> GetAllAsync(string? userId = null);
        Task<Models.Task?> GetByIdAsync(string id);
        Task<Models.Task> CreateAsync(Models.Task task);
        Task<Models.Task> UpdateAsync(Models.Task task);
        Task<bool> DeleteAsync(string id);
        Task<IEnumerable<Models.Task>> GetByUserIdAsync(string userId);
        Task<IEnumerable<Models.Task>> GetByStatusAsync(string status);
        Task<IEnumerable<Models.Task>> GetByNetworkIdAsync(string networkId);
    }
}

