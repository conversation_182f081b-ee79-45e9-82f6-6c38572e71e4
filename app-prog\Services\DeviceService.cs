using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;
using Task = System.Threading.Tasks.Task;

namespace NetworkManagement.Services
{
    public class DeviceService : IDeviceService
    {
        private readonly IServiceProvider _serviceProvider;

        public DeviceService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<IEnumerable<Device>> GetAllAsync(string? networkFilter = null)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var query = context.Devices.Include(d => d.Site).Include(d => d.Network).AsQueryable();

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(d => d.NetworkId == networkFilter);
                }

                return await query.OrderBy(d => d.Location).ToListAsync().ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Device?> GetByIdAsync(string id)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    return null;

                return await context.Devices
                    .Include(d => d.Site)
                    .Include(d => d.Network)
                    .FirstOrDefaultAsync(d => d.Id == id)
                    .ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Device> CreateAsync(Device device)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                device.Id = Guid.NewGuid().ToString();
                context.Devices.Add(device);
                await context.SaveChangesAsync().ConfigureAwait(false);
                return device;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Device> UpdateAsync(Device device)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                context.Devices.Update(device);
                await context.SaveChangesAsync().ConfigureAwait(false);
                return device;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var device = await context.Devices.FindAsync(id).ConfigureAwait(false);
                if (device == null) return false;

                context.Devices.Remove(device);
                await context.SaveChangesAsync().ConfigureAwait(false);
                return true;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Device>> SearchAsync(string searchTerm, string? networkFilter = null)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var query = context.Devices.Include(d => d.Site).Include(d => d.Network).AsQueryable();

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(d => d.NetworkId == networkFilter);
                }

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(d =>
                        (d.Location ?? "").Contains(searchTerm) ||
                        (d.Type ?? "").Contains(searchTerm) ||
                        (d.Ip ?? "").Contains(searchTerm) ||
                        (d.Responsible ?? "").Contains(searchTerm));
                }

                return await query.OrderBy(d => d.Location).ToListAsync().ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Device>> GetBySiteIdAsync(string siteId)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                return await context.Devices
                    .Where(d => d.SiteId == siteId)
                    .OrderBy(d => d.Location)
                    .ToListAsync()
                    .ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Device>> GetByStatusAsync(string status, string? networkFilter = null)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var query = context.Devices.Include(d => d.Site).Include(d => d.Network).AsQueryable();

                if (!string.IsNullOrEmpty(networkFilter))
                {
                    query = query.Where(d => d.NetworkId == networkFilter);
                }

                query = query.Where(d => d.Status == status);

                return await query.OrderBy(d => d.Location).ToListAsync().ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<IEnumerable<Device>> GetDevicesWithIpAsync()
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                return await context.Devices
                    .Include(d => d.Site)
                    .Where(d => !string.IsNullOrEmpty(d.Ip))
                    .OrderBy(d => d.Location)
                    .ToListAsync()
                    .ConfigureAwait(false);
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<Device> UpdateDeviceStatusAsync(string deviceId, string status)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                var device = await context.Devices.FindAsync(deviceId).ConfigureAwait(false);
                if (device == null)
                    throw new ArgumentException("الجهاز غير موجود", nameof(deviceId));

                device.Status = status;
                device.LastCheck = DateTime.Now;

                await context.SaveChangesAsync().ConfigureAwait(false);
                return device;
            }
            finally
            {
                scope.Dispose();
            }
        }

        public async Task<int> UpdateMultipleDeviceStatusesAsync(Dictionary<string, string> deviceStatuses)
        {
            var scope = _serviceProvider.CreateScope();
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Devices == null)
                    throw new InvalidOperationException("DbSet<Device> is null");

                int updatedCount = 0;

                foreach (var kvp in deviceStatuses)
                {
                    var device = await context.Devices.FindAsync(kvp.Key).ConfigureAwait(false);
                    if (device != null)
                    {
                        device.Status = kvp.Value;
                        device.LastCheck = DateTime.Now;
                        updatedCount++;
                    }
                }

                await context.SaveChangesAsync().ConfigureAwait(false);
                return updatedCount;
            }
            finally
            {
                scope.Dispose();
            }
        }


    }
}
