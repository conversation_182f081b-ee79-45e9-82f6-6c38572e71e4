using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public interface IInventoryService
    {
        Task<IEnumerable<Inventory>> GetAllAsync(string? networkFilter = null);
        Task<Inventory?> GetByIdAsync(string id);
        Task<Inventory> CreateAsync(Inventory inventory);
        Task<Inventory> UpdateAsync(Inventory inventory);
        Task<bool> DeleteAsync(string id);
        Task<IEnumerable<Inventory>> SearchAsync(string searchTerm, string? networkFilter = null);
        Task<IEnumerable<Inventory>> GetLowStockItemsAsync(string? networkFilter = null);
    }
}

