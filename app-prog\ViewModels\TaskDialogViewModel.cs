using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.ViewModels
{
    public partial class TaskDialogViewModel : ObservableObject
    {
        private readonly ITaskService _taskService;
        private readonly IAuthService _authService;
        private readonly INetworkService _networkService;

        [ObservableProperty]
        private string title = "إضافة مهمة جديدة";

        [ObservableProperty]
        private string description = string.Empty;

        [ObservableProperty]
        private string notes = string.Empty;

        [ObservableProperty]
        private string selectedStatus = "pending";

        [ObservableProperty]
        private string selectedPriority = "medium";

        [ObservableProperty]
        private DateTime? dueDate = DateTime.Now;

        [ObservableProperty]
        private string selectedNetworkId = string.Empty;

        [ObservableProperty]
        private ObservableCollection<Network> networks = new();

        [ObservableProperty]
        private bool isEditMode = false;

        [ObservableProperty]
        private bool isSaving = false;

        public ObservableCollection<string> StatusOptions { get; } = new()
        {
            "معلق",
            "قيد التنفيذ",
            "مكتمل",
            "ملغي"
        };

        public ObservableCollection<string> PriorityOptions { get; } = new()
        {
            "منخفضة",
            "متوسطة",
            "عالية",
            "عاجلة"
        };

        private Models.Task? _editingTask;
        public bool DialogResult { get; private set; }

        public TaskDialogViewModel(ITaskService taskService, IAuthService authService, INetworkService networkService)
        {
            _taskService = taskService;
            _authService = authService;
            _networkService = networkService;
        }

        public async System.Threading.Tasks.Task InitializeAsync(Models.Task? task = null)
        {
            try
            {
                // Load networks
                var allNetworks = await _networkService.GetAllAsync();
                Networks.Clear();
                foreach (var network in allNetworks)
                {
                    Networks.Add(network);
                }

                // Set default network (اختياري)
                var currentUser = _authService.CurrentUser;
                if (currentUser?.Role?.Equals("Admin", StringComparison.OrdinalIgnoreCase) != true && !string.IsNullOrEmpty(currentUser?.NetworkId))
                {
                    // للمدراء: تعيين شبكتهم كافتراضي
                    SelectedNetworkId = currentUser.NetworkId;
                }
                // للأدمن: ترك الحقل فارغ ليختار بنفسه

                // If editing existing task
                if (task != null)
                {
                    _editingTask = task;
                    IsEditMode = true;
                    Title = "تعديل المهمة";

                    Description = task.Description;
                    Notes = task.Notes ?? string.Empty;
                    SelectedStatus = GetStatusDisplay(task.Status);
                    SelectedPriority = GetPriorityDisplay(task.Priority ?? "medium");
                    DueDate = task.DueDate;
                    SelectedNetworkId = task.NetworkId ?? string.Empty;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing task dialog: {ex.Message}");
                MessageBox.Show(
                    $"خطأ في تحميل البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SaveAsync()
        {
            if (!await ValidateInputAsync())
                return;

            IsSaving = true;
            try
            {
                var currentUser = _authService.CurrentUser;

                if (IsEditMode && _editingTask != null)
                {
                    // التحقق من صلاحية تعديل المهمة
                    if (!_authService.CanEditData(_editingTask.NetworkId))
                    {
                        MessageBox.Show(
                            "ليس لديك صلاحية لتعديل هذه المهمة",
                            "غير مسموح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    // Update existing task
                    _editingTask.Description = Description;
                    _editingTask.Notes = Notes;
                    _editingTask.Status = GetStatusKey(SelectedStatus);
                    _editingTask.Priority = GetPriorityKey(SelectedPriority);
                    _editingTask.DueDate = DueDate;
                    _editingTask.NetworkId = SelectedNetworkId;
                    _editingTask.UpdatedAt = DateTime.Now;

                    await _taskService.UpdateAsync(_editingTask);
                }
                else
                {
                    // التحقق من صلاحية إضافة مهمة جديدة
                    var targetNetworkId = GetNetworkIdForNewTask();
                    if (!_authService.CanAddData(targetNetworkId))
                    {
                        MessageBox.Show(
                            "ليس لديك صلاحية لإضافة مهام في هذه الشبكة",
                            "غير مسموح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    // Create new task - استخدام null للـ foreign keys إذا لم تكن متاحة
                    var newTask = new Models.Task
                    {
                        Id = Guid.NewGuid().ToString(),
                        Description = Description,
                        Notes = Notes,
                        Status = GetStatusKey(SelectedStatus),
                        Priority = GetPriorityKey(SelectedPriority),
                        DueDate = DueDate,
                        NetworkId = targetNetworkId,
                        UserId = currentUser?.Id ?? "admin",
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    await _taskService.CreateAsync(newTask);
                }

                DialogResult = true;
                RequestClose?.Invoke();
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ في حفظ المهمة:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }

                System.Diagnostics.Debug.WriteLine($"Task save error: {ex}");

                MessageBox.Show(
                    errorMessage,
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsSaving = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            RequestClose?.Invoke();
        }

        private async System.Threading.Tasks.Task<bool> ValidateInputAsync()
        {
            if (string.IsNullOrWhiteSpace(Description))
            {
                MessageBox.Show("وصف المهمة مطلوب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // التحقق من وجود الشبكة فقط إذا كانت محددة
            if (!string.IsNullOrEmpty(SelectedNetworkId))
            {
                try
                {
                    var network = await _networkService.GetByIdAsync(SelectedNetworkId);
                    if (network == null)
                    {
                        MessageBox.Show("الشبكة المحددة غير موجودة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في التحقق من الشبكة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }
            }

            return true;
        }

        private static string GetStatusKey(string statusDisplay)
        {
            return statusDisplay switch
            {
                "معلق" => "pending",
                "قيد التنفيذ" => "in-progress",
                "مكتمل" => "completed",
                "ملغي" => "cancelled",
                _ => "pending"
            };
        }

        private static string GetPriorityKey(string priorityDisplay)
        {
            return priorityDisplay switch
            {
                "منخفضة" => "low",
                "متوسطة" => "medium",
                "عالية" => "high",
                "عاجلة" => "urgent",
                _ => "medium"
            };
        }

        private static string GetStatusDisplay(string statusKey)
        {
            return statusKey switch
            {
                "pending" => "معلق",
                "in-progress" => "قيد التنفيذ",
                "completed" => "مكتمل",
                "cancelled" => "ملغي",
                _ => "معلق"
            };
        }

        private static string GetPriorityDisplay(string priorityKey)
        {
            return priorityKey switch
            {
                "low" => "منخفضة",
                "medium" => "متوسطة",
                "high" => "عالية",
                "urgent" => "عاجلة",
                _ => "متوسطة"
            };
        }

        private string? GetNetworkIdForNewTask()
        {
            if (!string.IsNullOrEmpty(SelectedNetworkId))
            {
                return SelectedNetworkId;
            }

            // إذا كان المستخدم Network Manager، استخدم شبكته تلقائ<|im_start|>
            var currentUser = _authService.CurrentUser;
            if (_authService.IsNetworkManager && !string.IsNullOrEmpty(currentUser?.NetworkId))
            {
                return currentUser.NetworkId;
            }

            return null;
        }

        public event Action? RequestClose;
    }
}
