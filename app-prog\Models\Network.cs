using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NetworkManagement.Models
{
    public class Network
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = "";

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(20)]
        public string Color { get; set; } = "#2196F3";

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
        public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
        public virtual ICollection<Device> Devices { get; set; } = new List<Device>();
        public virtual ICollection<Models.Task> Tasks { get; set; } = new List<Models.Task>();
        public virtual ICollection<Purchase> Purchases { get; set; } = new List<Purchase>();
        public virtual ICollection<Inventory> Inventories { get; set; } = new List<Inventory>();

        // Display properties
        public string DisplayName => $"{Name} ({(IsActive ? "نشط" : "غير نشط")})";
    }
}
