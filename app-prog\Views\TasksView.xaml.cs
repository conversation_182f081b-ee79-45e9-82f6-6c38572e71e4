using System;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class TasksView : UserControl, IDisposable
    {
        private IServiceScope? _scope;
        private TasksViewModel? _viewModel;

        public TasksView()
        {
            InitializeComponent();
            Loaded += TasksView_Loaded;
            Unloaded += TasksView_Unloaded;
        }

        private async void TasksView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("TasksView_Loaded: بدء تحميل صفحة المهام");

                if (_viewModel == null && _scope == null)
                {
                    _scope = App.CreateScope();
                    _viewModel = _scope.ServiceProvider.GetRequiredService<TasksViewModel>();
                    DataContext = _viewModel;

                    System.Diagnostics.Debug.WriteLine("TasksView_Loaded: تم إنشاء TasksViewModel");
                    await _viewModel.InitializeAsync();
                    System.Diagnostics.Debug.WriteLine("TasksView_Loaded: تم تحميل الصفحة بنجاح");
                }
                else if (_viewModel != null)
                {
                    // إذا كان الـ ViewModel موجود بالفعل، قم بتحديث البيانات فقط
                    System.Diagnostics.Debug.WriteLine("TasksView_Loaded: تحديث البيانات الموجودة");
                    await _viewModel.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TasksView_Loaded: خطأ - {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء تحميل المهام:\n{ex.Message}",
                    "خطأ في تحميل المهام",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        private void TasksView_Unloaded(object sender, System.Windows.RoutedEventArgs e)
        {
            Dispose();
        }

        public void Dispose()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("TasksView: بدء تنظيف الموارد");

                if (_viewModel != null)
                {
                    _viewModel.Dispose();
                    _viewModel = null;
                    DataContext = null;
                }

                if (_scope != null)
                {
                    _scope.Dispose();
                    _scope = null;
                }

                System.Diagnostics.Debug.WriteLine("TasksView: تم تنظيف الموارد بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TasksView: خطأ في تنظيف الموارد - {ex.Message}");
            }
            GC.SuppressFinalize(this);
        }
    }
}
