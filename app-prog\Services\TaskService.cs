using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Data;
using NetworkManagement.Models;
using Task = System.Threading.Tasks.Task;

namespace NetworkManagement.Services
{
    public class TaskService : ITaskService
    {
        private readonly NetworkDbContext _context;

        public TaskService(NetworkDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Models.Task>> GetAllAsync(string? userId = null)
        {
            if (_context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            var query = _context.Tasks.Include(t => t.User).AsQueryable();

            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(t => t.UserId == userId);
            }

            return await query.OrderByDescending(t => t.CreatedAt).ToListAsync();
        }

        public async Task<Models.Task?> GetByIdAsync(string id)
        {
            if (_context.Tasks == null)
                return null;

            return await _context.Tasks
                .Include(t => t.User)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<Models.Task> CreateAsync(Models.Task task)
        {
            if (_context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            task.Id = Guid.NewGuid().ToString();
            task.CreatedAt = DateTime.Now;
            _context.Tasks.Add(task);
            await _context.SaveChangesAsync();
            return task;
        }

        public async Task<Models.Task> UpdateAsync(Models.Task task)
        {
            if (_context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            _context.Tasks.Update(task);
            await _context.SaveChangesAsync();
            return task;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            if (_context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            var task = await _context.Tasks.FindAsync(id);
            if (task == null) return false;

            _context.Tasks.Remove(task);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Models.Task>> GetByUserIdAsync(string userId)
        {
            if (_context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            return await _context.Tasks
                .Include(t => t.User)
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Models.Task>> GetByStatusAsync(string status)
        {
            if (_context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            return await _context.Tasks
                .Include(t => t.User)
                .Where(t => t.Status == status)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Models.Task>> GetByNetworkIdAsync(string networkId)
        {
            if (_context.Tasks == null)
                throw new InvalidOperationException("DbSet<Task> is null");

            return await _context.Tasks
                .Include(t => t.User)
                .Where(t => t.NetworkId == networkId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }
    }
}

