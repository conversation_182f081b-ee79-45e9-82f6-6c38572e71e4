<UserControl x:Class="NetworkManagement.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <!-- Page Header -->
            <TextBlock Text="إعدادات التطبيق" Style="{StaticResource PageHeaderStyle}" Margin="0,0,0,20"/>

            <!-- Loading Indicator -->
            <ProgressBar IsIndeterminate="True"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Height="4" Margin="0,0,0,10"/>

            <!-- Application Info -->
            <materialDesign:Card Margin="0,0,0,20">
                <StackPanel Margin="20">
                    <TextBlock Text="معلومات التطبيق"
                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                              Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="اسم التطبيق:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding ApplicationName}" Margin="0,0,0,15"/>

                            <TextBlock Text="الإصدار:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding ApplicationVersion}" Margin="0,0,0,15"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="خادم قاعدة البيانات:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding MySQLServer}" TextWrapping="Wrap" Margin="0,0,0,15"/>

                            <TextBlock Text="حجم قاعدة البيانات:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding DatabaseSizeDisplay}" Margin="0,0,0,15"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Statistics -->
            <materialDesign:Card Margin="0,0,0,20">
                <StackPanel Margin="20">
                    <TextBlock Text="إحصائيات البيانات"
                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                              Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Router" Width="32" Height="32"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalDevices}" FontSize="24" FontWeight="Bold"
                                      HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="الأجهزة" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="MapMarker" Width="32" Height="32"
                                                   Foreground="#4CAF50"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalSites}" FontSize="24" FontWeight="Bold"
                                      HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="المواقع" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="AccountGroup" Width="32" Height="32"
                                                   Foreground="#FF9800"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalUsers}" FontSize="24" FontWeight="Bold"
                                      HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="المستخدمين" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="32" Height="32"
                                                   Foreground="#9C27B0"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalPurchases}" FontSize="24" FontWeight="Bold"
                                      HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="المشتريات" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Package" Width="32" Height="32"
                                                   Foreground="#607D8B"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalInventoryItems}" FontSize="24" FontWeight="Bold"
                                      HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="المخزون" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- General Settings -->
            <materialDesign:Card Margin="0,0,0,20">
                <StackPanel Margin="20">
                    <TextBlock Text="الإعدادات العامة"
                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                              Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <CheckBox Content="تذكر بيانات تسجيل الدخول"
                                     IsChecked="{Binding RememberLoginEnabled}"
                                     Margin="0,0,0,15"/>

                            <CheckBox Content="إظهار الإشعارات"
                                     IsChecked="{Binding ShowNotifications}"
                                     Margin="0,0,0,15"/>

                            <TextBlock Text="الشبكة الافتراضية:" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding DefaultNetwork}"
                                    materialDesign:HintAssist.Hint="اختياري"
                                    Margin="0,0,0,15"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="مهلة فحص الاتصال (ثانية):" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding PingTimeoutSeconds}"
                                    materialDesign:HintAssist.Hint="5"
                                    Margin="0,0,0,15"/>

                            <TextBlock Text="المظهر:" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding AvailableThemes}"
                                     SelectedItem="{Binding Theme}"
                                     Margin="0,0,0,15"/>

                            <TextBlock Text="اللغة:" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding AvailableLanguages}"
                                     SelectedItem="{Binding Language}"
                                     Margin="0,0,0,15"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Database Settings -->
            <materialDesign:Card Margin="0,0,0,20">
                <StackPanel Margin="20">
                    <TextBlock Text="إعدادات قاعدة البيانات MySQL"
                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                              Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="الخادم:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding MySQLServer}" Margin="0,0,0,10"
                                    materialDesign:HintAssist.Hint="localhost"/>

                            <TextBlock Text="قاعدة البيانات:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding MySQLDatabase}" Margin="0,0,0,10"
                                    materialDesign:HintAssist.Hint="shabakaty"/>

                            <TextBlock Text="المستخدم:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding MySQLUser}" Margin="0,0,0,10"
                                    materialDesign:HintAssist.Hint="root"/>

                            <TextBlock Text="كلمة المرور:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <PasswordBox x:Name="MySQLPasswordBox" Margin="0,0,0,15"
                                         materialDesign:HintAssist.Hint="كلمة المرور"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <materialDesign:PackIcon Kind="DatabaseCheck" Width="24" Height="24"
                                                       Foreground="{Binding IsDatabaseConnected, Converter={StaticResource BooleanToColorConverter}, ConverterParameter='Green,Red'}"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="حالة الاتصال: " FontWeight="Medium"/>
                                <TextBlock Text="{Binding DatabaseConnectionStatus}"
                                           Foreground="{Binding IsDatabaseConnected, Converter={StaticResource BooleanToColorConverter}, ConverterParameter='Green,Red'}"/>
                            </StackPanel>

                            <Button Command="{Binding TestDatabaseConnectionCommand}"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Margin="0,0,0,15">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabaseSync" Margin="0,0,8,0"/>
                                    <TextBlock Text="فحص الاتصال"/>
                                </StackPanel>
                            </Button>

                            <Button Command="{Binding CreateBackupCommand}"
                                   Style="{StaticResource MaterialDesignRaisedButton}"
                                   Margin="0,0,0,10">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabaseExport" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="إنشاء نسخة احتياطية"/>
                                </StackPanel>
                            </Button>

                            <!-- Database Maintenance Section -->
                            <Separator Margin="0,15,0,15"/>
                            <TextBlock Text="صيانة قاعدة البيانات" FontWeight="Bold"
                                      Foreground="Orange" Margin="0,0,0,10"/>

                            <Button Command="{Binding RepairDatabaseCommand}"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   BorderBrush="Orange" Foreground="Orange"
                                   IsEnabled="{Binding IsDatabaseConnected}"
                                   Margin="0,0,0,15">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabaseCog" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="إصلاح البيانات التالفة"/>
                                </StackPanel>
                            </Button>


                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>



            <!-- Backup Settings -->
            <materialDesign:Card Margin="0,0,0,20">
                <StackPanel Margin="20">
                    <TextBlock Text="إعدادات النسخ الاحتياطي"
                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                              Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="آخر نسخة احتياطية:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding LastBackupDateDisplay}" Margin="0,0,0,15"/>

                            <CheckBox Content="تفعيل النسخ الاحتياطي التلقائي"
                                     IsChecked="{Binding AutoBackupEnabled}"
                                     Margin="0,0,0,15"/>

                            <TextBlock Text="تكرار النسخ الاحتياطي (أيام):" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding AutoBackupOptions}"
                                     SelectedItem="{Binding AutoBackupDays}"
                                     IsEnabled="{Binding AutoBackupEnabled}"
                                     Margin="0,0,0,15"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="مجلد النسخ الاحتياطية:" Margin="0,0,0,5"/>
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" Text="{Binding BackupLocation}" IsReadOnly="True"/>
                                <Button Grid.Column="1" Content="تصفح"
                                       Command="{Binding SelectBackupLocationCommand}"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Margin="10,0,0,0"/>
                            </Grid>

                            <StackPanel Orientation="Horizontal">
                                <Button Command="{Binding CreateBackupCommand}"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Margin="0,0,10,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Backup" Width="16" Height="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="إنشاء نسخة احتياطية"/>
                                    </StackPanel>
                                </Button>

                                <Button Command="{Binding RestoreBackupCommand}"
                                       Style="{StaticResource MaterialDesignOutlinedButton}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Restore" Width="16" Height="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="استعادة"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Command="{Binding SaveSettingsCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Margin="0,0,15,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="حفظ الإعدادات"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding LoadSettingsCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,15,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إعادة تحميل"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding ResetSettingsCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Restore" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إعادة تعيين"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl>
