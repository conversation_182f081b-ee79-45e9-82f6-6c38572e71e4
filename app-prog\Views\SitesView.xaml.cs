using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class SitesView : UserControl
    {
        public SitesView()
        {
            InitializeComponent();
            Loaded += SitesView_Loaded;
        }

        private async void SitesView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                if (DataContext is SitesViewModel viewModel)
                {
                    await viewModel.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SitesView_Loaded: خطأ - {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"خطأ في تحميل صفحة المواقع:\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
