using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class UserService : IUserService
    {
        private readonly NetworkDbContext _context;
        private readonly IPasswordHashService _passwordHashService;

        public UserService(NetworkDbContext context, IPasswordHashService passwordHashService)
        {
            _context = context;
            _passwordHashService = passwordHashService;
        }

        public async Task<IEnumerable<User>> GetAllAsync(string? networkFilter = null)
        {
            if (_context.Users == null)
            {
                return Array.Empty<User>();
            }

            var query = _context.Users.Include(u => u.Network).AsQueryable();

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(u => u.NetworkId == networkFilter);
            }

            return await query.OrderBy(u => u.Name).ToListAsync();
        }

        public async Task<User?> GetByIdAsync(string id)
        {
            if (_context.Users == null)
            {
                return null;
            }

            return await _context.Users.Include(u => u.Network).FirstOrDefaultAsync(u => u.Id == id);
        }

        public async Task<User> CreateAsync(User user)
        {
            if (_context.Users == null)
            {
                throw new InvalidOperationException("DbSet<User> is null");
            }

            // التحقق من عدم وجود مستخدم بنفس الاسم
            var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == user.Username);
            if (existingUser != null)
            {
                throw new InvalidOperationException($"مستخدم بالاسم '{user.Username}' موجود بالفعل");
            }

            // تشفير كلمة المرور قبل الحفظ
            if (!string.IsNullOrEmpty(user.Password))
            {
                user.Password = _passwordHashService.HashPassword(user.Password);
            }

            // استخدام Username كـ ID بدلاً من GUID
            user.Id = user.Username;
            user.CreatedAt = DateTime.Now;
            user.UpdatedAt = DateTime.Now;

            _context.Users.Add(user);
            await _context.SaveChangesAsync();
            return user;
        }

        public async Task<User> UpdateAsync(User user)
        {
            if (_context.Users == null)
            {
                throw new InvalidOperationException("DbSet<User> is null");
            }

            // الحصول على المستخدم الحالي من قاعدة البيانات
            var existingUser = await _context.Users.FindAsync(user.Id);
            if (existingUser == null)
            {
                throw new InvalidOperationException("المستخدم غير موجود");
            }

            // تحديث البيانات الأساسية
            existingUser.Name = user.Name;
            existingUser.Role = user.Role;
            existingUser.NetworkId = user.NetworkId;
            existingUser.Email = user.Email;
            existingUser.IsActive = user.IsActive;
            existingUser.UpdatedAt = DateTime.Now;

            // تحديث كلمة المرور فقط إذا تم تمرير كلمة مرور جديدة
            if (!string.IsNullOrEmpty(user.Password))
            {
                // التحقق من أن كلمة المرور ليست مشفرة بالفعل
                if (!_passwordHashService.IsPasswordHashed(user.Password))
                {
                    existingUser.Password = _passwordHashService.HashPassword(user.Password);
                }
                else
                {
                    existingUser.Password = user.Password;
                }
            }

            await _context.SaveChangesAsync();
            return existingUser;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            if (_context.Users == null)
            {
                return false;
            }

            var user = await _context.Users.FindAsync(id);
            if (user == null) return false;

            _context.Users.Remove(user);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<User>> SearchAsync(string searchTerm, string? networkFilter = null)
        {
            if (_context.Users == null)
            {
                return Array.Empty<User>();
            }

            var query = _context.Users.Include(u => u.Network).AsQueryable();

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(u => u.NetworkId == networkFilter);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(u =>
                    u.Name.Contains(searchTerm) ||
                    u.Username.Contains(searchTerm) ||
                    u.Role.Contains(searchTerm));
            }

            return await query.OrderBy(u => u.Name).ToListAsync();
        }
    }
}

