using NetworkManagement.ViewModels;
using System.Windows;

namespace NetworkManagement.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            try
            {
                DataContext = App.GetService<MainViewModel>();
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"فشل في إنشاء MainViewModel: {ex.Message}");
                throw;
            }
        }
    }
}
