@echo off
chcp 65001 >nul
echo ========================================
echo    بناء مثبت Shabaka Pro الاحترافي
echo    Building Shabaka Pro Professional Installer
echo ========================================
echo.

echo [1/4] تنظيف الملفات القديمة...
echo [1/4] Cleaning old files...
if exist "Output\*.exe" del /q "Output\*.exe"
if exist "dist\*" rmdir /s /q "dist"
echo ✓ تم التنظيف بنجاح

echo.
echo [2/4] بناء التطبيق...
echo [2/4] Building application...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=false -o dist
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في بناء التطبيق
    echo ❌ Failed to build application
    pause
    exit /b 1
)
echo ✓ تم بناء التطبيق بنجاح

echo.
echo [3/4] إنشاء المثبت باستخدام Inno Setup...
echo [3/4] Creating installer using Inno Setup...

:: البحث عن Inno Setup في المواقع الشائعة
set "INNO_PATH="
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
if exist "C:\Program Files\Inno Setup 6\ISCC.exe" set "INNO_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" set "INNO_PATH=C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
if exist "C:\Program Files\Inno Setup 5\ISCC.exe" set "INNO_PATH=C:\Program Files\Inno Setup 5\ISCC.exe"

if "%INNO_PATH%"=="" (
    echo ❌ لم يتم العثور على Inno Setup
    echo ❌ Inno Setup not found
    echo يرجى التأكد من تثبيت Inno Setup في المسار الافتراضي
    echo Please make sure Inno Setup is installed in the default path
    pause
    exit /b 1
)

echo تم العثور على Inno Setup في: %INNO_PATH%
echo Found Inno Setup at: %INNO_PATH%

"%INNO_PATH%" "ShabakaPro-Setup.iss"
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في إنشاء المثبت
    echo ❌ Failed to create installer
    pause
    exit /b 1
)
echo ✓ تم إنشاء المثبت بنجاح

echo.
echo [4/4] التحقق من الملف النهائي...
echo [4/4] Verifying final file...
if exist "Output\ShabakaPro-Setup-v1.0.0.exe" (
    echo ✓ تم إنشاء المثبت بنجاح!
    echo ✓ Installer created successfully!
    echo.
    echo 📁 موقع الملف: Output\ShabakaPro-Setup-v1.0.0.exe
    echo 📁 File location: Output\ShabakaPro-Setup-v1.0.0.exe
    echo.
    echo 🎉 المثبت جاهز للاستخدام!
    echo 🎉 Installer is ready to use!
) else (
    echo ❌ لم يتم العثور على ملف المثبت
    echo ❌ Installer file not found
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul