using System;

namespace NetworkManagement.Models
{
    /// <summary>
    /// فئة إعدادات التطبيق
    /// </summary>
    public class AppSettings
    {
        /// <summary>
        /// تذكر بيانات تسجيل الدخول
        /// </summary>
        public bool RememberLogin { get; set; } = true;

        /// <summary>
        /// الشبكة الافتراضية
        /// </summary>
        public string? DefaultNetwork { get; set; }

        /// <summary>
        /// مهلة الـ Ping بالثواني
        /// </summary>
        public int PingTimeout { get; set; } = 5;

        /// <summary>
        /// إظهار الإشعارات
        /// </summary>
        public bool ShowNotifications { get; set; } = true;

        /// <summary>
        /// المظهر (Light/Dark)
        /// </summary>
        public string Theme { get; set; } = "Light";

        /// <summary>
        /// اللغة
        /// </summary>
        public string Language { get; set; } = "العربية";

        /// <summary>
        /// مسار النسخ الاحتياطية
        /// </summary>
        public string BackupLocation { get; set; } = "";

        /// <summary>
        /// تفعيل النسخ الاحتياطي التلقائي
        /// </summary>
        public bool AutoBackupEnabled { get; set; } = false;

        /// <summary>
        /// عدد أيام النسخ الاحتياطي التلقائي
        /// </summary>
        public int AutoBackupDays { get; set; } = 7;

        /// <summary>
        /// تاريخ آخر نسخة احتياطية
        /// </summary>
        public DateTime? LastBackupDate { get; set; }

        /// <summary>
        /// خادم MySQL
        /// </summary>
        public string MySQLServer { get; set; } = "localhost";

        /// <summary>
        /// قاعدة بيانات MySQL
        /// </summary>
        public string MySQLDatabase { get; set; } = "shabakaty";

        /// <summary>
        /// مستخدم MySQL
        /// </summary>
        public string MySQLUser { get; set; } = "root";

        /// <summary>
        /// كلمة مرور MySQL
        /// </summary>
        public string MySQLPassword { get; set; } = "";

        /// <summary>
        /// منفذ MySQL
        /// </summary>
        public int MySQLPort { get; set; } = 3306;

        /// <summary>
        /// استخدام SSL في الاتصال
        /// </summary>
        public bool MySQLUseSSL { get; set; } = false;

        /// <summary>
        /// مهلة الاتصال بقاعدة البيانات بالثواني
        /// </summary>
        public int DatabaseTimeout { get; set; } = 30;

        /// <summary>
        /// تفعيل تسجيل الأخطاء
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// مستوى تسجيل الأخطاء
        /// </summary>
        public string LogLevel { get; set; } = "Information";

        /// <summary>
        /// مسار ملف السجل
        /// </summary>
        public string LogFilePath { get; set; } = "";

        /// <summary>
        /// تفعيل التحديث التلقائي
        /// </summary>
        public bool AutoUpdateEnabled { get; set; } = true;

        /// <summary>
        /// فترة التحقق من التحديثات بالساعات
        /// </summary>
        public int UpdateCheckInterval { get; set; } = 24;

        /// <summary>
        /// آخر تحقق من التحديثات
        /// </summary>
        public DateTime? LastUpdateCheck { get; set; }

        /// <summary>
        /// إنشاء إعدادات افتراضية
        /// </summary>
        /// <returns>إعدادات افتراضية</returns>
        public static AppSettings CreateDefault()
        {
            return new AppSettings
            {
                RememberLogin = true,
                DefaultNetwork = "",
                PingTimeout = 5,
                ShowNotifications = true,
                Theme = "Light",
                Language = "العربية",
                BackupLocation = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    "NetworkManagement_Backups"),
                AutoBackupEnabled = false,
                AutoBackupDays = 7,
                MySQLServer = "localhost",
                MySQLDatabase = "shabakaty",
                MySQLUser = "root",
                MySQLPassword = "",
                MySQLPort = 3306,
                MySQLUseSSL = false,
                DatabaseTimeout = 30,
                EnableLogging = true,
                LogLevel = "Information",
                LogFilePath = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "NetworkManagement", "Logs", "app.log"),
                AutoUpdateEnabled = true,
                UpdateCheckInterval = 24
            };
        }

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        /// <returns>true إذا كانت الإعدادات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(MySQLServer) &&
                   !string.IsNullOrWhiteSpace(MySQLDatabase) &&
                   !string.IsNullOrWhiteSpace(MySQLUser) &&
                   PingTimeout > 0 &&
                   AutoBackupDays > 0 &&
                   MySQLPort > 0 &&
                   DatabaseTimeout > 0 &&
                   UpdateCheckInterval > 0;
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>سلسلة الاتصال</returns>
        public string GetConnectionString()
        {
            var builder = new MySqlConnector.MySqlConnectionStringBuilder
            {
                Server = MySQLServer,
                Database = MySQLDatabase,
                UserID = MySQLUser,
                Password = MySQLPassword,
                Port = (uint)MySQLPort,
                SslMode = MySQLUseSSL ? MySqlConnector.MySqlSslMode.Required : MySqlConnector.MySqlSslMode.None,
                ConnectionTimeout = (uint)DatabaseTimeout,
                CharacterSet = "utf8mb4",
                AllowUserVariables = true,
                UseAffectedRows = false
            };

            return builder.ConnectionString;
        }
    }
}
