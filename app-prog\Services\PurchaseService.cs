using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class PurchaseService : IPurchaseService
    {
        private readonly NetworkDbContext _context;

        public PurchaseService(NetworkDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Purchase>> GetAllAsync(string? networkFilter = null)
        {
            var query = _context.Purchases?.Include(p => p.Network).AsQueryable() ?? throw new InvalidOperationException("DbSet<Purchase> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(p => p.NetworkId == networkFilter);
            }

            return await query.OrderByDescending(p => p.Date).ToListAsync();
        }

        public async Task<Purchase?> GetByIdAsync(string id)
        {
            return await _context.Purchases!.FindAsync(id);
        }

        public async Task<Purchase> CreateAsync(Purchase purchase)
        {
            purchase.Id = Guid.NewGuid().ToString();
            purchase.CreatedAt = DateTime.Now;
            _context.Purchases!.Add(purchase);
            await _context.SaveChangesAsync();
            return purchase;
        }

        public async Task<Purchase> UpdateAsync(Purchase purchase)
        {
            _context.Purchases!.Update(purchase);
            await _context.SaveChangesAsync();
            return purchase;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            var purchase = await _context.Purchases!.FindAsync(id);
            if (purchase == null) return false;

            _context.Purchases.Remove(purchase);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Purchase>> SearchAsync(string searchTerm, string? networkFilter = null)
        {
            var query = _context.Purchases?.Include(p => p.Network).AsQueryable() ?? throw new InvalidOperationException("DbSet<Purchase> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(p => p.NetworkId == networkFilter);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(p =>
                    p.ItemType.Contains(searchTerm) ||
                    (p.Supplier ?? "").Contains(searchTerm) ||
                    (p.Description ?? "").Contains(searchTerm));
            }

            return await query.OrderByDescending(p => p.Date).ToListAsync();
        }

        public async Task<decimal> GetTotalSpentAsync(string? networkFilter = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Purchases?.AsQueryable() ?? throw new InvalidOperationException("DbSet<Purchase> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(p => p.NetworkId == networkFilter);
            }

            if (startDate.HasValue)
            {
                query = query.Where(p => p.Date >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(p => p.Date <= endDate.Value);
            }

            return await query.SumAsync(p => p.Price); // السعر كما هو مكتوب (إجمالي للكمية)
        }
    }
}

