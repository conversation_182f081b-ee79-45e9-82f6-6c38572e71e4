using System.Windows.Controls;

namespace NetworkManagement.Views
{
    public partial class SettingsView : UserControl
    {
        public SettingsView()
        {
            InitializeComponent();
        }

        private void SaveButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            // TODO: Save settings to appsettings.json
            var viewModel = (ViewModels.SettingsViewModel)DataContext;
            viewModel.SaveSettingsCommand.Execute(null);
        }
    }
}
