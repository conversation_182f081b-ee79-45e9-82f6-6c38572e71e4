using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.ViewModels;
using NetworkManagement.Helpers;

namespace NetworkManagement.ViewModels
{
    public partial class ReportsViewModel : ObservableObject
    {
        private readonly IAuthService _authService;
        private readonly IServiceProvider _serviceProvider;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private DateTime? startDate;

        [ObservableProperty]
        private DateTime? endDate;

        [ObservableProperty]
        private string selectedReportType = "ملخص عام";

        [ObservableProperty]
        private string? selectedNetworkId;

        [ObservableProperty]
        private string? selectedUserId;

        [ObservableProperty]
        private ObservableCollection<Models.Network> availableNetworks = new();

        [ObservableProperty]
        private ObservableCollection<Models.User> availableUsers = new();

        // General Statistics
        [ObservableProperty]
        private int totalDevices;

        [ObservableProperty]
        private int activeDevices;

        [ObservableProperty]
        private int totalSites;

        [ObservableProperty]
        private int totalUsers;

        [ObservableProperty]
        private decimal totalPurchases;

        [ObservableProperty]
        private int totalInventoryItems;

        [ObservableProperty]
        private int lowStockItems;

        // Device Statistics by Network
        [ObservableProperty]
        private ObservableCollection<NetworkStatistic> networkStatistics = new();

        // Monthly Purchase Trends
        [ObservableProperty]
        private ObservableCollection<MonthlyPurchase> monthlyPurchases = new();

        // Device Status Distribution
        [ObservableProperty]
        private ObservableCollection<DeviceStatusCount> deviceStatusCounts = new();

        // Report Data Collections
        [ObservableProperty]
        private ObservableCollection<Models.User> reportUsers = new();

        [ObservableProperty]
        private ObservableCollection<Models.Device> reportDevices = new();

        [ObservableProperty]
        private ObservableCollection<Models.Site> reportSites = new();

        [ObservableProperty]
        private ObservableCollection<Models.Task> reportTasks = new();

        [ObservableProperty]
        private ObservableCollection<Models.Purchase> reportPurchases = new();

        [ObservableProperty]
        private ObservableCollection<Models.Inventory> reportInventory = new();

        // Visibility properties for different report types
        [ObservableProperty]
        private bool isUsersReportVisible = false;

        [ObservableProperty]
        private bool isDevicesReportVisible = false;

        [ObservableProperty]
        private bool isSitesReportVisible = false;

        [ObservableProperty]
        private bool isTasksReportVisible = false;

        [ObservableProperty]
        private bool isPurchasesReportVisible = false;

        [ObservableProperty]
        private bool isInventoryReportVisible = false;

        public string[] ReportTypes { get; } = {
            "ملخص عام",
            "تقرير الأجهزة",
            "تقرير المواقع",
            "تقرير المستخدمين",
            "تقرير المهام",
            "تقرير المشتريات",
            "تقرير المخزون"
        };

        // Permission properties
        public bool CanViewReports => _authService.CanViewReports;
        public bool CanExportReports => _authService.CanViewReports; // Same as view for now
        public bool CanPrintReports => _authService.CanViewReports; // Same as view for now

        public ReportsViewModel(IAuthService authService, IServiceProvider serviceProvider)
        {
            _authService = authService;
            _serviceProvider = serviceProvider;

            // Subscribe to user changes to update permissions
            _authService.UserChanged += OnUserChanged;

            // Set default date range (current month)
            var now = DateTime.Now;
            StartDate = new DateTime(now.Year, now.Month, 1);
            EndDate = StartDate.Value.AddMonths(1).AddDays(-1);

            _ = InitializeAsync(); // Fire and forget - safe for initial load
        }

        private async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                await LoadNetworksAndUsersAsync();
                await LoadReportsAsync();
            }
            catch
            {
                // Handle initialization error silently
            }
        }

        private async System.Threading.Tasks.Task LoadNetworksAndUsersAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var networkService = scope.ServiceProvider.GetRequiredService<INetworkService>();
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

                // تحميل الشبكات
                var networks = await networkService.GetAllAsync().ConfigureAwait(false);
                AvailableNetworks.Clear();
                AvailableNetworks.Add(new Models.Network { Id = "", Name = "جميع الشبكات" });
                foreach (var network in networks)
                {
                    AvailableNetworks.Add(network);
                }

                // تحميل المستخدمين
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var users = await userService.GetAllAsync(networkFilter).ConfigureAwait(false);
                AvailableUsers.Clear();
                AvailableUsers.Add(new Models.User { Id = "0", Name = "جميع المستخدمين", Username = "" });
                foreach (var user in users)
                {
                    AvailableUsers.Add(user);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks and users: {ex.Message}");
            }
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanViewReports));
                    OnPropertyChanged(nameof(CanExportReports));
                    OnPropertyChanged(nameof(CanPrintReports));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadReportsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadReportsAsync()
        {
            try
            {
                IsLoading = true;
                await LoadGeneralStatisticsAsync();
                await LoadNetworkStatisticsAsync();
                await LoadMonthlyPurchasesAsync();
                await LoadDeviceStatusCountsAsync();
            }
            catch
            {
                // Handle loading error silently
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task GenerateReportAsync()
        {
            try
            {
                IsLoading = true;

                switch (SelectedReportType)
                {
                    case "ملخص عام":
                        await LoadGeneralStatisticsAsync();
                        HideAllReports();
                        break;
                    case "تقرير الأجهزة":
                        await LoadDeviceReportAsync();
                        break;
                    case "تقرير المواقع":
                        await LoadSiteReportAsync();
                        break;
                    case "تقرير المستخدمين":
                        await LoadUserReportAsync();
                        break;
                    case "تقرير المهام":
                        await LoadTaskReportAsync();
                        break;
                    case "تقرير المشتريات":
                        await LoadPurchaseReportAsync();
                        break;
                    case "تقرير المخزون":
                        await LoadInventoryReportAsync();
                        break;
                    default:
                        await LoadReportsAsync();
                        HideAllReports();
                        break;
                }

                System.Windows.MessageBox.Show(
                    $"تم إنشاء تقرير '{SelectedReportType}' بنجاح.",
                    "تم إنشاء التقرير",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء إنشاء التقرير:\n{ex.Message}",
                    "خطأ في التقرير",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ExportReportAsync()
        {
            try
            {
                // التحقق من صلاحية التصدير
                if (!CanExportReports)
                {
                    PermissionHelper.ShowPermissionDeniedMessage("تصدير", "التقارير");
                    return;
                }

                IsLoading = true;

                using var scope = _serviceProvider.CreateScope();
                var exportService = scope.ServiceProvider.GetRequiredService<IReportExportService>();

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var defaultFileName = $"تقرير_{SelectedReportType}_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await exportService.GetSaveFilePathAsync(defaultFileName, filter).ConfigureAwait(false);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                switch (SelectedReportType)
                {
                    case "تقرير الأجهزة":
                        var devices = await GetFilteredDevicesAsync();
                        result = isExcel
                            ? await exportService.ExportDevicesReportToExcelAsync(devices, filePath).ConfigureAwait(false)
                            : await exportService.ExportDevicesReportToCsvAsync(devices, filePath).ConfigureAwait(false);
                        break;

                    case "تقرير المواقع":
                        var sites = await GetFilteredSitesAsync();
                        result = isExcel
                            ? await exportService.ExportSitesReportToExcelAsync(sites, filePath).ConfigureAwait(false)
                            : await exportService.ExportSitesReportToCsvAsync(sites, filePath).ConfigureAwait(false);
                        break;

                    case "تقرير المستخدمين":
                        var users = await GetFilteredUsersAsync();
                        result = isExcel
                            ? await exportService.ExportUsersReportToExcelAsync(users, filePath).ConfigureAwait(false)
                            : await exportService.ExportUsersReportToCsvAsync(users, filePath).ConfigureAwait(false);
                        break;

                    case "تقرير المهام":
                        var tasks = await GetFilteredTasksAsync();
                        result = isExcel
                            ? await exportService.ExportTasksReportToExcelAsync(tasks, filePath).ConfigureAwait(false)
                            : await exportService.ExportTasksReportToCsvAsync(tasks, filePath).ConfigureAwait(false);
                        break;

                    case "تقرير المشتريات":
                        var purchases = await GetFilteredPurchasesAsync();
                        result = isExcel
                            ? await exportService.ExportPurchasesReportToExcelAsync(purchases, filePath).ConfigureAwait(false)
                            : await exportService.ExportPurchasesReportToCsvAsync(purchases, filePath).ConfigureAwait(false);
                        break;

                    case "تقرير المخزون":
                        var inventory = await GetFilteredInventoryAsync();
                        result = isExcel
                            ? await exportService.ExportInventoryReportToExcelAsync(inventory, filePath).ConfigureAwait(false)
                            : await exportService.ExportInventoryReportToCsvAsync(inventory, filePath).ConfigureAwait(false);
                        break;

                    default: // ملخص عام
                        result = isExcel
                            ? await exportService.ExportCompleteReportToExcelAsync(this, filePath).ConfigureAwait(false)
                            : await exportService.ExportGeneralReportToCsvAsync(this, filePath).ConfigureAwait(false);
                        break;
                }

                if (!string.IsNullOrEmpty(result))
                {
                    System.Windows.MessageBox.Show(
                        $"تم تصدير التقرير بنجاح إلى:\n{result}",
                        "تم التصدير",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء تصدير التقرير:\n{ex.Message}",
                    "خطأ في التصدير",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ExportProfessionalExcelAsync()
        {
            try
            {
                // التحقق من صلاحية التصدير
                if (!CanExportReports)
                {
                    PermissionHelper.ShowPermissionDeniedMessage("تصدير", "التقارير المتقدمة");
                    return;
                }

                IsLoading = true;

                using var scope = _serviceProvider.CreateScope();
                var professionalReportService = scope.ServiceProvider.GetRequiredService<IProfessionalReportService>();

                var defaultFileName = $"تقرير_{SelectedReportType}_{DateTime.Now:yyyy-MM-dd}.xlsx";
                var filePath = await professionalReportService.GetSaveFilePathAsync(defaultFileName, "Excel Files (*.xlsx)|*.xlsx").ConfigureAwait(false);

                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                switch (SelectedReportType)
                {
                    case "تقرير الأجهزة":
                        var devices = await GetFilteredDevicesAsync();
                        result = await professionalReportService.GenerateDevicesExcelReportAsync(devices, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المستخدمين":
                        var users = await GetFilteredUsersAsync();
                        result = await professionalReportService.GenerateUsersExcelReportAsync(users, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المهام":
                        var tasks = await GetFilteredTasksAsync();
                        result = await professionalReportService.GenerateTasksExcelReportAsync(tasks, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المواقع":
                        var sites = await GetFilteredSitesAsync();
                        result = await professionalReportService.GenerateSitesExcelReportAsync(sites, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المخزون":
                        var inventory = await GetFilteredInventoryAsync();
                        result = await professionalReportService.GenerateInventoryExcelReportAsync(inventory, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المشتريات":
                        var purchases = await GetFilteredPurchasesAsync();
                        result = await professionalReportService.GeneratePurchasesExcelReportAsync(purchases, filePath).ConfigureAwait(false);
                        break;
                    default:
                        result = await professionalReportService.GenerateStatisticsExcelReportAsync(filePath).ConfigureAwait(false);
                        break;
                }

                if (!string.IsNullOrEmpty(result))
                {
                    var openResult = System.Windows.MessageBox.Show(
                        $"تم إنشاء التقرير بنجاح!\n\nهل تريد فتح الملف؟",
                        "تم إنشاء التقرير",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Information);

                    if (openResult == System.Windows.MessageBoxResult.Yes)
                    {
                        await professionalReportService.OpenFileAsync(result).ConfigureAwait(false);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء إنشاء التقرير:\n{ex.Message}",
                    "خطأ في التقرير",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ExportProfessionalPdfAsync()
        {
            try
            {
                // التحقق من صلاحية التصدير
                if (!CanExportReports)
                {
                    PermissionHelper.ShowPermissionDeniedMessage("تصدير", "التقارير المتقدمة");
                    return;
                }

                IsLoading = true;

                using var scope = _serviceProvider.CreateScope();
                var professionalReportService = scope.ServiceProvider.GetRequiredService<IProfessionalReportService>();

                var defaultFileName = $"تقرير_{SelectedReportType}_{DateTime.Now:yyyy-MM-dd}.pdf";
                var filePath = await professionalReportService.GetSaveFilePathAsync(defaultFileName, "PDF Files (*.pdf)|*.pdf").ConfigureAwait(false);

                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                switch (SelectedReportType)
                {
                    case "تقرير الأجهزة":
                        var devices = await GetFilteredDevicesAsync();
                        result = await professionalReportService.GenerateDevicesPdfReportAsync(devices, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المستخدمين":
                        var users = await GetFilteredUsersAsync();
                        result = await professionalReportService.GenerateUsersPdfReportAsync(users, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المهام":
                        var tasks = await GetFilteredTasksAsync();
                        result = await professionalReportService.GenerateTasksPdfReportAsync(tasks, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المواقع":
                        var sites = await GetFilteredSitesAsync();
                        result = await professionalReportService.GenerateSitesPdfReportAsync(sites, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المخزون":
                        var inventory = await GetFilteredInventoryAsync();
                        result = await professionalReportService.GenerateInventoryPdfReportAsync(inventory, filePath).ConfigureAwait(false);
                        break;
                    case "تقرير المشتريات":
                        var purchases = await GetFilteredPurchasesAsync();
                        result = await professionalReportService.GeneratePurchasesPdfReportAsync(purchases, filePath).ConfigureAwait(false);
                        break;
                    default:
                        result = await professionalReportService.GenerateStatisticsPdfReportAsync(filePath).ConfigureAwait(false);
                        break;
                }

                if (!string.IsNullOrEmpty(result))
                {
                    var openResult = System.Windows.MessageBox.Show(
                        $"تم إنشاء التقرير بنجاح!\n\nهل تريد فتح الملف؟",
                        "تم إنشاء التقرير",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Information);

                    if (openResult == System.Windows.MessageBoxResult.Yes)
                    {
                        await professionalReportService.OpenFileAsync(result).ConfigureAwait(false);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء إنشاء التقرير:\n{ex.Message}",
                    "خطأ في التقرير",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task PrintProfessionalReportAsync()
        {
            try
            {
                // التحقق من صلاحية الطباعة
                if (!CanPrintReports)
                {
                    PermissionHelper.ShowPermissionDeniedMessage("طباعة", "التقارير المتقدمة");
                    return;
                }

                IsLoading = true;

                using var scope = _serviceProvider.CreateScope();
                var professionalReportService = scope.ServiceProvider.GetRequiredService<IProfessionalReportService>();

                switch (SelectedReportType)
                {
                    case "تقرير الأجهزة":
                        var devices = await GetFilteredDevicesAsync();
                        await professionalReportService.PrintDevicesReportAsync(devices).ConfigureAwait(false);
                        break;
                    case "تقرير المستخدمين":
                        var users = await GetFilteredUsersAsync();
                        await professionalReportService.PrintUsersReportAsync(users).ConfigureAwait(false);
                        break;
                    case "تقرير المهام":
                        var tasks = await GetFilteredTasksAsync();
                        await professionalReportService.PrintTasksReportAsync(tasks).ConfigureAwait(false);
                        break;
                    case "تقرير المواقع":
                        var sites = await GetFilteredSitesAsync();
                        await professionalReportService.PrintSitesReportAsync(sites).ConfigureAwait(false);
                        break;
                    case "تقرير المخزون":
                        var inventory = await GetFilteredInventoryAsync();
                        await professionalReportService.PrintInventoryReportAsync(inventory).ConfigureAwait(false);
                        break;
                    case "تقرير المشتريات":
                        var purchases = await GetFilteredPurchasesAsync();
                        await professionalReportService.PrintPurchasesReportAsync(purchases).ConfigureAwait(false);
                        break;
                    default:
                        await professionalReportService.PrintStatisticsReportAsync().ConfigureAwait(false);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء الطباعة:\n{ex.Message}",
                    "خطأ في الطباعة",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task PreviewReportAsync()
        {
            try
            {
                // التحقق من صلاحية المعاينة
                if (!CanViewReports)
                {
                    PermissionHelper.ShowPermissionDeniedMessage("معاينة", "التقارير");
                    return;
                }

                IsLoading = true;

                using var scope = _serviceProvider.CreateScope();
                var professionalReportService = scope.ServiceProvider.GetRequiredService<IProfessionalReportService>();

                switch (SelectedReportType)
                {
                    case "تقرير الأجهزة":
                        var devices = await GetFilteredDevicesAsync();
                        await professionalReportService.PreviewDevicesReportAsync(devices).ConfigureAwait(false);
                        break;
                    case "تقرير المستخدمين":
                        var users = await GetFilteredUsersAsync();
                        await professionalReportService.PreviewUsersReportAsync(users).ConfigureAwait(false);
                        break;
                    case "تقرير المهام":
                        var tasks = await GetFilteredTasksAsync();
                        await professionalReportService.PreviewTasksReportAsync(tasks).ConfigureAwait(false);
                        break;
                    case "تقرير المواقع":
                        var sites = await GetFilteredSitesAsync();
                        await professionalReportService.PreviewSitesReportAsync(sites).ConfigureAwait(false);
                        break;
                    case "تقرير المخزون":
                        var inventory = await GetFilteredInventoryAsync();
                        await professionalReportService.PreviewInventoryReportAsync(inventory).ConfigureAwait(false);
                        break;
                    case "تقرير المشتريات":
                        var purchases = await GetFilteredPurchasesAsync();
                        await professionalReportService.PreviewPurchasesReportAsync(purchases).ConfigureAwait(false);
                        break;
                    default:
                        await professionalReportService.PreviewStatisticsReportAsync().ConfigureAwait(false);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء معاينة التقرير:\n{ex.Message}",
                    "خطأ في المعاينة",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async System.Threading.Tasks.Task LoadGeneralStatisticsAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceService>();
                var siteService = scope.ServiceProvider.GetRequiredService<ISiteService>();
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
                var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();
                var inventoryService = scope.ServiceProvider.GetRequiredService<IInventoryService>();

                // استخدام PermissionHelper للحصول على فلتر الشبكة
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);

                // Load device statistics
                var devices = await deviceService.GetAllAsync(networkFilter).ConfigureAwait(false);
                TotalDevices = devices.Count();
                ActiveDevices = devices.Count(d => d.Status == "نشط");

                // Load site statistics
                var sites = await siteService.GetAllAsync(networkFilter).ConfigureAwait(false);
                TotalSites = sites.Count();

                // Load user statistics
                var users = await userService.GetAllAsync(networkFilter).ConfigureAwait(false);
                TotalUsers = users.Count();

                // Load purchase statistics
                var purchases = await purchaseService.GetAllAsync(networkFilter).ConfigureAwait(false);
                if (StartDate.HasValue && EndDate.HasValue)
                {
                    purchases = purchases.Where(p => p.Date >= StartDate.Value && p.Date <= EndDate.Value);
                }
                TotalPurchases = purchases.Sum(p => p.Price);

                // Load inventory statistics
                var inventory = await inventoryService.GetAllAsync(networkFilter).ConfigureAwait(false);
                TotalInventoryItems = inventory.Count();

                var lowStock = await inventoryService.GetLowStockItemsAsync(networkFilter).ConfigureAwait(false);
                LowStockItems = lowStock.Count();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading general statistics: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task LoadNetworkStatisticsAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceService>();

                // استخدام PermissionHelper للحصول على فلتر الشبكة
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var devices = await deviceService.GetAllAsync(networkFilter).ConfigureAwait(false);
                var networkGroups = devices.GroupBy(d => d.Network?.Name ?? "غير محدد");

                NetworkStatistics.Clear();
                foreach (var group in networkGroups)
                {
                    NetworkStatistics.Add(new NetworkStatistic
                    {
                        NetworkName = group.Key,
                        DeviceCount = group.Count(),
                        ActiveCount = group.Count(d => d.Status == "نشط"),
                        InactiveCount = group.Count(d => d.Status != "نشط")
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading network statistics: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task LoadMonthlyPurchasesAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();

                // استخدام PermissionHelper للحصول على فلتر الشبكة
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var purchases = await purchaseService.GetAllAsync(networkFilter).ConfigureAwait(false);
                var monthlyGroups = purchases
                    .Where(p => p.Date >= DateTime.Now.AddMonths(-12))
                    .GroupBy(p => new { p.Date.Year, p.Date.Month })
                    .OrderBy(g => g.Key.Year).ThenBy(g => g.Key.Month);

                MonthlyPurchases.Clear();
                foreach (var group in monthlyGroups)
                {
                    MonthlyPurchases.Add(new MonthlyPurchase
                    {
                        Month = $"{group.Key.Month:00}/{group.Key.Year}",
                        Amount = group.Sum(p => p.Price),
                        Count = group.Count()
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading monthly purchases: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task LoadDeviceStatusCountsAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceService>();

                // استخدام PermissionHelper للحصول على فلتر الشبكة
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
                var devices = await deviceService.GetAllAsync(networkFilter).ConfigureAwait(false);
                var statusGroups = devices.GroupBy(d => d.Status ?? "غير محدد");

                DeviceStatusCounts.Clear();
                foreach (var group in statusGroups)
                {
                    DeviceStatusCounts.Add(new DeviceStatusCount
                    {
                        Status = group.Key,
                        Count = group.Count(),
                        Percentage = Math.Round((double)group.Count() / devices.Count() * 100, 1)
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading device status counts: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task LoadDeviceReportAsync()
        {
            await LoadGeneralStatisticsAsync();
            await LoadDeviceStatusCountsAsync();

            // تحميل بيانات الأجهزة للعرض
            var devices = await GetFilteredDevicesAsync();
            ReportDevices.Clear();
            foreach (var device in devices)
            {
                ReportDevices.Add(device);
            }

            // إظهار تقرير الأجهزة فقط
            HideAllReports();
            IsDevicesReportVisible = true;
        }

        private async System.Threading.Tasks.Task LoadSiteReportAsync()
        {
            await LoadGeneralStatisticsAsync();
            await LoadNetworkStatisticsAsync();

            // تحميل بيانات المواقع للعرض
            var sites = await GetFilteredSitesAsync();
            ReportSites.Clear();
            foreach (var site in sites)
            {
                ReportSites.Add(site);
            }

            // إظهار تقرير المواقع فقط
            HideAllReports();
            IsSitesReportVisible = true;
        }

        private async System.Threading.Tasks.Task LoadUserReportAsync()
        {
            await LoadGeneralStatisticsAsync();

            // تحميل بيانات المستخدمين للعرض
            var users = await GetFilteredUsersAsync();
            ReportUsers.Clear();
            foreach (var user in users)
            {
                ReportUsers.Add(user);
            }

            // إظهار تقرير المستخدمين فقط
            HideAllReports();
            IsUsersReportVisible = true;
        }

        private async System.Threading.Tasks.Task LoadPurchaseReportAsync()
        {
            await LoadGeneralStatisticsAsync();
            await LoadMonthlyPurchasesAsync();

            // تحميل بيانات المشتريات للعرض
            var purchases = await GetFilteredPurchasesAsync();
            ReportPurchases.Clear();
            foreach (var purchase in purchases)
            {
                ReportPurchases.Add(purchase);
            }

            // إظهار تقرير المشتريات فقط
            HideAllReports();
            IsPurchasesReportVisible = true;
        }

        private async System.Threading.Tasks.Task LoadInventoryReportAsync()
        {
            await LoadGeneralStatisticsAsync();

            // تحميل بيانات المخزون للعرض
            var inventory = await GetFilteredInventoryAsync();
            ReportInventory.Clear();
            foreach (var item in inventory)
            {
                ReportInventory.Add(item);
            }

            // إظهار تقرير المخزون فقط
            HideAllReports();
            IsInventoryReportVisible = true;
        }

        private async System.Threading.Tasks.Task LoadTaskReportAsync()
        {
            await LoadGeneralStatisticsAsync();

            // تحميل بيانات المهام للعرض
            var tasks = await GetFilteredTasksAsync();
            ReportTasks.Clear();
            foreach (var task in tasks)
            {
                ReportTasks.Add(task);
            }

            // إظهار تقرير المهام فقط
            HideAllReports();
            IsTasksReportVisible = true;
        }

        partial void OnStartDateChanged(DateTime? value)
        {
            _ = LoadReportsAsync();
        }

        partial void OnEndDateChanged(DateTime? value)
        {
            _ = LoadReportsAsync();
        }

        partial void OnSelectedReportTypeChanged(string value)
        {
            UpdateReportVisibility();
            _ = LoadReportsAsync();
        }

        private void UpdateReportVisibility()
        {
            // إخفاء جميع التقارير أولاً
            IsUsersReportVisible = false;
            IsDevicesReportVisible = false;
            IsSitesReportVisible = false;
            IsTasksReportVisible = false;
            IsPurchasesReportVisible = false;
            IsInventoryReportVisible = false;

            // إظهار التقرير المحدد
            switch (SelectedReportType)
            {
                case "تقرير المستخدمين":
                    IsUsersReportVisible = true;
                    break;
                case "تقرير الأجهزة":
                    IsDevicesReportVisible = true;
                    break;
                case "تقرير المواقع":
                    IsSitesReportVisible = true;
                    break;
                case "تقرير المهام":
                    IsTasksReportVisible = true;
                    break;
                case "تقرير المشتريات":
                    IsPurchasesReportVisible = true;
                    break;
                case "تقرير المخزون":
                    IsInventoryReportVisible = true;
                    break;
            }
        }

        partial void OnSelectedNetworkIdChanged(string? value)
        {
            _ = LoadReportsAsync();
        }

        partial void OnSelectedUserIdChanged(string? value)
        {
            _ = LoadReportsAsync();
        }

        // Helper methods for filtering data
        private async Task<IEnumerable<Models.Device>> GetFilteredDevicesAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceService>();

            // استخدام PermissionHelper للحصول على فلتر الشبكة
            var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
            var devices = await deviceService.GetAllAsync(networkFilter).ConfigureAwait(false);

            // ثم تطبيق فلاتر المستخدم
            if (!string.IsNullOrEmpty(SelectedNetworkId))
                devices = devices.Where(d => d.NetworkId == SelectedNetworkId);

            if (StartDate.HasValue && EndDate.HasValue)
                devices = devices.Where(d => d.CreatedAt >= StartDate.Value && d.CreatedAt <= EndDate.Value);

            return devices;
        }

        private async Task<IEnumerable<Models.Site>> GetFilteredSitesAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var siteService = scope.ServiceProvider.GetRequiredService<ISiteService>();

            // استخدام PermissionHelper للحصول على فلتر الشبكة
            var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
            var sites = await siteService.GetAllAsync(networkFilter).ConfigureAwait(false);

            // ثم تطبيق فلاتر المستخدم
            if (!string.IsNullOrEmpty(SelectedNetworkId))
                sites = sites.Where(s => s.NetworkId == SelectedNetworkId);

            if (StartDate.HasValue && EndDate.HasValue)
                sites = sites.Where(s => s.CreatedAt >= StartDate.Value && s.CreatedAt <= EndDate.Value);

            return sites;
        }

        private async Task<IEnumerable<Models.User>> GetFilteredUsersAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

            // استخدام PermissionHelper للحصول على فلتر الشبكة
            var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
            var users = await userService.GetAllAsync(networkFilter).ConfigureAwait(false);

            // ثم تطبيق فلاتر المستخدم
            if (!string.IsNullOrEmpty(SelectedNetworkId))
                users = users.Where(u => u.NetworkId == SelectedNetworkId);

            if (StartDate.HasValue && EndDate.HasValue)
                users = users.Where(u => u.CreatedAt >= StartDate.Value && u.CreatedAt <= EndDate.Value);

            return users;
        }

        private async Task<IEnumerable<Models.Task>> GetFilteredTasksAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

            // استخدام PermissionHelper للحصول على فلتر الشبكة
            var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
            var tasks = await taskService.GetAllAsync(networkFilter).ConfigureAwait(false);

            // ثم تطبيق فلاتر المستخدم
            if (!string.IsNullOrEmpty(SelectedNetworkId))
                tasks = tasks.Where(t => t.NetworkId == SelectedNetworkId);

            if (!string.IsNullOrEmpty(SelectedUserId) && SelectedUserId != "0")
                tasks = tasks.Where(t => t.UserId == SelectedUserId);

            if (StartDate.HasValue && EndDate.HasValue)
                tasks = tasks.Where(t => t.CreatedAt >= StartDate.Value && t.CreatedAt <= EndDate.Value);

            return tasks;
        }

        private async Task<IEnumerable<Models.Purchase>> GetFilteredPurchasesAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var purchaseService = scope.ServiceProvider.GetRequiredService<IPurchaseService>();

            // استخدام PermissionHelper للحصول على فلتر الشبكة
            var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
            var purchases = await purchaseService.GetAllAsync(networkFilter).ConfigureAwait(false);

            // ثم تطبيق فلاتر المستخدم
            if (!string.IsNullOrEmpty(SelectedNetworkId))
                purchases = purchases.Where(p => p.NetworkId == SelectedNetworkId);

            if (StartDate.HasValue && EndDate.HasValue)
                purchases = purchases.Where(p => p.Date >= StartDate.Value && p.Date <= EndDate.Value);

            return purchases;
        }

        private async Task<IEnumerable<Models.Inventory>> GetFilteredInventoryAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var inventoryService = scope.ServiceProvider.GetRequiredService<IInventoryService>();

            // استخدام PermissionHelper للحصول على فلتر الشبكة
            var networkFilter = PermissionHelper.GetNetworkFilter(_authService);
            var inventory = await inventoryService.GetAllAsync(networkFilter).ConfigureAwait(false);

            // ثم تطبيق فلاتر المستخدم
            if (!string.IsNullOrEmpty(SelectedNetworkId))
                inventory = inventory.Where(i => i.NetworkId == SelectedNetworkId);

            if (StartDate.HasValue && EndDate.HasValue)
                inventory = inventory.Where(i => i.CreatedAt >= StartDate.Value && i.CreatedAt <= EndDate.Value);

            return inventory;
        }

        // Helper methods for showing/hiding reports
        private void HideAllReports()
        {
            IsDevicesReportVisible = false;
            IsSitesReportVisible = false;
            IsUsersReportVisible = false;
            IsTasksReportVisible = false;
            IsPurchasesReportVisible = false;
            IsInventoryReportVisible = false;
        }
    }

    // Helper classes for statistics
    public class NetworkStatistic
    {
        public string NetworkName { get; set; } = string.Empty;
        public int DeviceCount { get; set; }
        public int ActiveCount { get; set; }
        public int InactiveCount { get; set; }
    }

    public class MonthlyPurchase
    {
        public string Month { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int Count { get; set; }
    }

    public class DeviceStatusCount
    {
        public string Status { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
    }
}
