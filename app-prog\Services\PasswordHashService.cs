using System;
using BCrypt.Net;

namespace NetworkManagement.Services
{
    /// <summary>
    /// خدمة تشفير كلمات المرور باستخدام BCrypt
    /// </summary>
    public class PasswordHashService : IPasswordHashService
    {
        private const int WorkFactor = 12; // قوة التشفير - كلما زادت كلما كان أكثر أماناً وأبطأ

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور الأصلية</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("كلمة المرور لا يمكن أن تكون فارغة", nameof(password));

            return BCrypt.Net.BCrypt.HashPassword(password, WorkFactor);
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور الأصلية</param>
        /// <param name="hashedPassword">كلمة المرور المشفرة</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة</returns>
        public bool VerifyPassword(string password, string hashedPassword)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hashedPassword))
                return false;

            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch (Exception)
            {
                // في حالة وجود خطأ في التحقق، نعتبر كلمة المرور خاطئة
                return false;
            }
        }

        /// <summary>
        /// التحقق من أن كلمة المرور مشفرة بالفعل
        /// BCrypt hashes always start with $2a$, $2b$, $2x$, or $2y$
        /// </summary>
        /// <param name="password">كلمة المرور للفحص</param>
        /// <returns>true إذا كانت مشفرة</returns>
        public bool IsPasswordHashed(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            // BCrypt hashes have a specific format and length
            return password.Length >= 60 && 
                   (password.StartsWith("$2a$") || 
                    password.StartsWith("$2b$") || 
                    password.StartsWith("$2x$") || 
                    password.StartsWith("$2y$"));
        }
    }
}
