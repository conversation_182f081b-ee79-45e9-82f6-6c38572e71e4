using System.Threading.Tasks;

namespace NetworkManagement.Services
{
    public interface ILocalizationService
    {
        /// <summary>
        /// تطبيق اللغة المحددة
        /// </summary>
        Task ApplyLanguageAsync(string languageCode);

        /// <summary>
        /// الحصول على اللغة الحالية
        /// </summary>
        string GetCurrentLanguage();

        /// <summary>
        /// الحصول على اللغات المتاحة
        /// </summary>
        string[] GetAvailableLanguages();

        /// <summary>
        /// تطبيق اللغة الافتراضية
        /// </summary>
        Task ApplyDefaultLanguageAsync();

        /// <summary>
        /// تهيئة اللغة من الإعدادات المحفوظة
        /// </summary>
        Task InitializeLanguageAsync();

        /// <summary>
        /// الحصول على النص المترجم
        /// </summary>
        string GetLocalizedString(string key);
    }
}
